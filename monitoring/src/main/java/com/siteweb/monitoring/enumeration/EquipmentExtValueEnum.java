package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 设备扩展字段 在层级ExtendedField中
 */
@Getter
@AllArgsConstructor
public enum EquipmentExtValueEnum {
    /**
     * 字节现场使用
     */
    TYPE("Type","类型",1),
    ALIAS("Alias","别名",1),
    EXLOCATION("Exlocation","扩展位置",1)
    ;

    private final String value;
    private final String name;
    /**
     * 1文本 2数字
     */
    private final Integer type;
}
