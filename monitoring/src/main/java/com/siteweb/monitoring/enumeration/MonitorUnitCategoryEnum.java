package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MonitorUnitCategoryEnum {
    RMU_MU(1, "RMU下MU"),
    IDU(2, "IDU"),
    IPLU(4, "IPLU"),
    IMU(5, "IMU"),
    E_STONE(6, "eStone"),
    IDU_X(7, "IDU-X"),
    ISU(8, "ISU"),
    FSU(9, "FSU"),
    B_INTERFACE(10, "BInterface"),
    CATCHER(11, "Catcher"),
    GFSU(12, "GFSU"),
    ISUV2(14, "ISUV2"),
    WORK_STATION(16, "WorkStation");

    private final int itemId;
    private final String itemValue;
}
