package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ControlResultTypeEnum {
    SUCCESS(1, "控制成功","command.status.success"),
    FAILURE(2, "控制失败","command.status.failure"),
    TIMEOUT(3, "处理超时","command.status.timeout"),
    NO_RESPONSE(4, "未返回","command.status.noResponse"),
    ADDRESS_ERROR(5, "控制单元地址错误","command.status.addressError"),
    PARAMETER_ERROR(6, "参数错误","command.status.parameterError"),
    QUEUE_TIMEOUT(7, "在控制队列中超时","command.status.queueTimeout"),
    RECORDING_ENDED(8, "录像结束","command.status.recordingEnded"),;
    private final int code;
    private final String description;
    private final String i18nCode;
}
