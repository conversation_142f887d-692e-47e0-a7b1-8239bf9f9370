package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AlarmOperationTypeEnum {
    START(1,"告警开始"),
    END(2,"告警结束"),
    CONFIRM(3,"告警确认"),
    NOTE(5,"告警备注");


    /**
     * 操作值
     */
    private final Integer value;
    /**
     * 描述
     */
    private final String description;

    public static String getOperationTypeNameByValue(int value) {
        for ( AlarmOperationTypeEnum e : AlarmOperationTypeEnum.values()) {
            if (e.getValue() == value) {
                return e.getDescription();
            }
        }
        return "";
    }
}
