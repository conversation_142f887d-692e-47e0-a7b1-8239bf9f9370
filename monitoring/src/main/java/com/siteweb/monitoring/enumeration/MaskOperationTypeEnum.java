package com.siteweb.monitoring.enumeration;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.fasterxml.jackson.annotation.JsonValue;

import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * 告警屏蔽操作类型
 *
 * @Author: lzy
 * @Date: 2022/5/26 9:39
 */
@SuppressWarnings("all")
public enum MaskOperationTypeEnum {

    ADD_MASK_OPERATION(1, "新增屏蔽"),
    CANCEL_MAST_OPERATION(2, "解除屏蔽");

    /**
     * 屏蔽类型
     */
    @EnumValue
    private final Integer operationType;

    /**
     * 屏蔽类型描述
     */
    @JsonValue
    private final String desc;

    MaskOperationTypeEnum(Integer operationType, String desc) {
        this.operationType = operationType;
        this.desc = desc;
    }

    public Integer getOperationType() {
        return operationType;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDesc(Integer operationType) {
        Optional<MaskOperationTypeEnum> first = Arrays.stream(values()).filter(e -> Objects.equals(e.getOperationType(), operationType)).findFirst();
        if (first.isPresent()) {
            return first.get().getDesc();
        }
        return "";
    }
}
