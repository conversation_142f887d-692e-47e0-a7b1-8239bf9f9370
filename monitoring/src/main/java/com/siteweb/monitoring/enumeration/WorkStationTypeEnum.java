package com.siteweb.monitoring.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Getter
@AllArgsConstructor
public enum WorkStationTypeEnum {
    APPLICATION_SERVER(1, "应用服务器(AS)"),
    DATA_SERVER(2, "数据服务器（DS）"),
    APPLICATION_SERVER_II(31, "应用服务器(AS)2"),
    BUSINESS_SERVER(20, "业务服务器(BS)"),
    BUSINESS_SERVER_II(32, "业务服务器(BS)2"),
    DATABASE_SERVER(6, "数据库服务器"),
    NOTIFICATION_SERVER(16, "通知服务器"),
    REALTIME_DATA_SERVER(23, "实时数据服务器"),
    MOBILE_INTERFACE_SERVER(24, "手机接口服务器"),
    RMU(8, "RMU"),
    BACK_GROUND_SERVER(22, "后台服务器（s6）"),
    JOB_SERVER(25, "定时任务服务");
    private final Integer value;
    private final String name;

    private static final Map<Integer, WorkStationTypeEnum> ENUM_MAP = new HashMap<>();
    static {
        ENUM_MAP.put(APPLICATION_SERVER.getValue(), APPLICATION_SERVER);
    }
    public static String getName(Integer value) {
        return Optional.ofNullable(ENUM_MAP.get(value)).map(WorkStationTypeEnum::getName).orElse("");
    }
}
