package com.siteweb.monitoring.enumeration;

/**
 * <AUTHOR> <PERSON>
 * @description DynamicConfigObjectType
 * @createTime 2022-06-13 08:36:28
 */
public enum DynamicConfigObjectType {
    Basic(0),
    Equipment(10),
    Singnal(20),//信号
    SingnalMeaning(21),//信号含义
    Event(30),//事件
    Control(40),
    Port(50),
    SamplerUnit(60),
    Condition(70),//事件条件
    Mask(80),
    LogAction(90),
    Action(92),
    InformUser(94),
    Application(200);

    private int value = 0;

    public int value() {
        return this.value;
    }

    DynamicConfigObjectType(int value) {
        this.value = value;
    }

    public static DynamicConfigObjectType valueOf(int value) {
        switch (value) {
            case 0:
                return Basic;
            case 10:
                return Equipment;
            case 20:
                return Singnal;
            case 21:
                return SingnalMeaning;
            case 30:
                return Event;
            case 40:
                return Control;
            case 50:
                return Port;
            case 60:
                return SamplerUnit;
            case 70:
                return Condition;
            case 80:
                return Mask;
            case 90:
                return LogAction;
            case 92:
                return Action;
            case 94:
                return InformUser;
            case 200:
                return Application;
            default:
                return null;
        }
    }
}
