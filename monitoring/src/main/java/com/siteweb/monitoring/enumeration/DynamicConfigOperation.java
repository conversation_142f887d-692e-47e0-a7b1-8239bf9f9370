package com.siteweb.monitoring.enumeration;

/**
 * <AUTHOR> z<PERSON>
 * @description DynamicConfigOpreation
 * @createTime 2022-06-13 08:54:09
 */
public enum DynamicConfigOperation {
    Add(1),
    Modify(2),
    Delete(3);

    private int value = 1;

    public int value() {
        return this.value;
    }

    DynamicConfigOperation(int value) {
        this.value = value;
    }

    public static DynamicConfigOperation valueOf(int value) {
        switch (value) {
            case 1:
                return Add;
            case 2:
                return Modify;
            case 3:
                return Delete;
            default:
                return null;
        }
    }
}
