<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.SignalPropertyMapper">
    <insert id="insertSignalProperty">
        INSERT INTO TBL_SignalProperty(EquipmentTemplateId, SignalId, SignalPropertyId)
        VALUES(#{equipmentTemplateId}, #{signalId}, #{signalPropertyId})
    </insert>
    <insert id="batchInsert">
        INSERT INTO TBL_SignalProperty(equipmenttemplateid, signalid, signalpropertyid) VALUES
        <foreach collection="signalProperties" item="item" separator=",">
            (#{item.equipmentTemplateId},#{item.signalId},#{item.signalPropertyId})
        </foreach>
    </insert>
    <delete id="deleteSignalProperty">
        DELETE FROM TBL_SignalProperty WHERE EquipmentTemplateId = #{equipmentTemplateId} AND SignalId = #{signalId} AND SignalPropertyId = #{signalPropertyId}
    </delete>
    <select id="findSignalPropertyIdsBySignalId" resultType="java.lang.Integer">
        SELECT a.SignalPropertyId FROM TBL_SignalProperty a INNER JOIN TBL_Equipment b
        ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE a.SignalId = #{signalId}
        AND b.EquipmentId = #{equipmentId}
    </select>
    <select id="findSignalPropertiesByEquipmentIdAndSignalIds" resultType="com.siteweb.monitoring.dto.EquipmentSignalPropertyDTO">
        SELECT a.id, a.equipmenttemplateid, a.signalid, a.signalpropertyid,b.EquipmentId FROM tbl_signalproperty a
        INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateid
        WHERE b.EquipmentId =#{equipmentId} AND signalId IN
        <foreach collection="signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>