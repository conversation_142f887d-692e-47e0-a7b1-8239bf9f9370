<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.MonitorUnitMapper">
    <select id="getAllMonitorUnit" resultType="com.siteweb.monitoring.entity.MonitorUnit">
        SELECT a.MonitorUnitId,  a.MonitorUnitName,  a.MonitorUnitCategory,  a.MonitorUnitCode,  a.WorkStationId,
        a.StationId,  a.Ip<PERSON>ddress,  a.RunMode,  a.ConfigFileCode,  a.ConfigUpdateTime,  a.SampleConfigCode,
        a.SoftwareVersion,  a.Description,  a.StartTime,  a.HeartbeatTime, a.ConnectState,  a.UpdateTime,  a.IsSync,
        a.SyncTime,  a.IsConfigOK,  a.ConfigFileCode_Old,  a.<PERSON>pleConfigCode_Old,  a.<PERSON><PERSON>,
        a.<PERSON>ri<PERSON>,  a.Enable,  a.ProjectName,  a.ContractNo,  a.InstallTime,b.ItemValue AS monitorUnitCategoryName
        FROM tsl_monitorunit a inner join TBL_DataItem b on a.MonitorUnitCategory = b.ItemId and b.EntryId = 34;
    </select>
    <select id="getFsuIpList" resultType="java.lang.String">
        SELECT IpAddress
        FROM TSL_MonitorUnit
        WHERE MonitorUnitCategory NOT IN (0, 1, 3, 24)
    </select>
</mapper>