<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.HouseMapper">
    <select id="findHouseInfo" resultType="com.siteweb.monitoring.dto.HouseInfoDTO">
        SELECT s.stationid, s.stationname, h.houseid, h.housename, h.description, h.lastupdatedate
        FROM tbl_house h
        INNER JOIN tbl_station s ON h.stationid = s.stationid
        <where>
            <if test="stationIdList != null and stationIdList.size() > 0">
                h.stationid IN
                <foreach collection="stationIdList" item="stationId" open="(" separator="," close=")">
                    #{stationId}
                </foreach>
            </if>
        </where>
    </select>
</mapper>