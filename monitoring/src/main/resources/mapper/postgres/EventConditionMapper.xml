<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EventConditionMapper">
    <insert id="insertEventCondition">
        INSERT INTO TBL_EventCondition ( EventConditionId, EquipmentTemplateId, EventId, StartOperation,
        StartCompareValue, StartDelay, EndOperation, EndCompareValue, EndDelay, Frequency, FrequencyThreshold, Meanings,
        EquipmentState, BaseTypeId, EventSeverity, StandardName)
        VALUES (#{eventConditionId}, #{equipmentTemplateId}, #{eventId}, #{startOperation},
        #{startCompareValue}, #{startDelay}, #{endOperation}, #{endCompareValue}, #{endDelay}, #{frequency},
        #{frequencyThreshold}, #{meanings}, #{equipmentState}, #{baseTypeId}, #{eventSeverity}, #{standardName})
    </insert>
    <insert id="batchInsert">
        INSERT INTO TBL_EventCondition(eventconditionid, equipmenttemplateid, eventid, startoperation,
        startcomparevalue, startdelay, endoperation, endcomparevalue, enddelay,
        frequency, frequencythreshold, meanings, equipmentstate, basetypeid,
        eventseverity, standardname) VALUES
        <foreach collection="eventConditionList" item="item" separator=",">
          (#{item.eventConditionId},#{item.equipmentTemplateId},#{item.eventId},#{item.startOperation},
           #{item.startCompareValue},#{item.startDelay},#{item.endOperation},#{item.endCompareValue},#{item.endDelay},
           #{item.frequency},#{item.frequencyThreshold},#{item.meanings},#{item.equipmentState},#{item.baseTypeId},
           #{item.eventSeverity},#{item.standardName})
        </foreach>
    </insert>
    <update id="updateEventCondition">
        UPDATE TBL_EventCondition
        <set>
            <if test="startOperation != null">StartOperation = #{startOperation},</if>
            <if test="startCompareValue != null">StartCompareValue = #{startCompareValue},</if>
            <if test="startDelay != null">StartDelay = #{startDelay},</if>
            <if test="endOperation != null">EndOperation = #{endOperation},</if>
            <if test="endCompareValue != null">EndCompareValue = #{endCompareValue},</if>
            <if test="endDelay != null">EndDelay = #{endDelay},</if>
            <if test="frequency != null">Frequency = #{frequency},</if>
            <if test="frequencyThreshold != null">FrequencyThreshold = #{frequencyThreshold},</if>
            <if test="meanings != null">Meanings = #{meanings},</if>
            <if test="eventSeverity != null">EventSeverity = #{eventSeverity},</if>
            <if test="baseTypeId != null">BaseTypeId = #{baseTypeId},</if>
            <if test="equipmentState != null">EquipmentState = #{equipmentState}</if>
        </set>
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND EventId = #{eventId} AND EventConditionId =
        #{eventConditionId}
    </update>
    <delete id="deleteEventConditionById">
        DELETE FROM TBL_EventCondition
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND EventId = #{eventId} AND EventConditionId =
        #{eventConditionId}
    </delete>
    <select id="findEventConditionDTOByEventId" resultType="com.siteweb.monitoring.dto.EventConditionDTO">
        SELECT a.Id, a.EventConditionId, a.EquipmentTemplateId, a.EventId, a.StartOperation, a.StartCompareValue,
        a.StartDelay,
        a.EndOperation, a.EndCompareValue, a.EndDelay, a.Frequency, a.FrequencyThreshold, a.Meanings, a.BaseTypeId,
        a.EventSeverity,a.EquipmentState,
        b.StationId, c.ItemValue as EventSeverityName, d.BaseTypeName, b.EquipmentId
        FROM TBL_EventCondition a INNER JOIN TBL_Equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        LEFT JOIN TBL_DataItem c on c.EntryId = 23 and a.EventSeverity = c.ItemId
        LEFT JOIN TBL_EventBaseDic d on a.BaseTypeId = d.BaseTypeId
        WHERE b.EquipmentId = #{equipmentId} and a.EventId = #{eventId}
    </select>
    <select id="findEventConditionDTOBySignalId" resultType="com.siteweb.monitoring.dto.EventConditionDTO">
        SELECT a.Id, a.EventConditionId, a.EquipmentTemplateId, e.EventId, a.StartOperation, a.StartCompareValue,
        a.StartDelay,
        a.EndOperation, a.EndCompareValue, a.EndDelay, a.Frequency, a.FrequencyThreshold, a.Meanings, a.BaseTypeId,
        a.EventSeverity,a.EquipmentState,
        b.StationId, c.ItemValue as EventSeverityName, d.BaseTypeName, b.EquipmentId
        FROM TBL_EventCondition a INNER JOIN TBL_Equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        INNER JOIN TBL_Event e ON a.EquipmentTemplateId = e.EquipmentTemplateId AND a.EventId = e.EventId
        LEFT JOIN TBL_DataItem c on c.EntryId = 23 and a.EventSeverity = c.ItemId
        LEFT JOIN TBL_EventBaseDic d on a.BaseTypeId = d.BaseTypeId
        WHERE b.EquipmentId = #{equipmentId} and e.SignalId = #{signalId}
    </select>
    <select id="findEventConditionByEquipmentIdAndEventIds" resultType="com.siteweb.monitoring.dto.EquipmentEventConditionDTO">
        SELECT a.*,b.EquipmentId FROM tbl_eventcondition a inner JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateid
        WHERE b.EquipmentId = #{equipmentId} AND eventId IN
        <foreach collection="eventIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findEventConditionByEquipmentId" resultType="com.siteweb.monitoring.dto.EvenConditionDTO">
        SELECT b.EventId, b.EventName, c.EventConditionId, c.Meanings
        FROM TBL_Equipment a
                 INNER JOIN tbl_event b ON b.EquipmentTemplateId = a.EquipmentTemplateId
                 INNER JOIN tbl_eventcondition c ON c.EquipmentTemplateId = b.EquipmentTemplateid AND b.EventId = c.EventId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findByEquipmentIdAndEventIdAndEventConditionId" resultType="com.siteweb.monitoring.entity.EventCondition">
        SELECT a.Id,
               a.EventConditionId,
               a.EquipmentTemplateId,
               a.EventId,
               a.StartOperation,
               a.StartCompareValue,
               a.StartDelay,
               a.EndOperation,
               a.EndCompareValue,
               a.EndDelay,
               a.Frequency,
               a.FrequencyThreshold,
               a.Meanings,
               a.EquipmentState,
               a.BaseTypeId,
               a.EventSeverity,
               a.StandardName
        FROM TBL_EventCondition a,
             TBL_Equipment b
        WHERE b.EquipmentId = #{equipmentId}
          AND a.EventId = #{eventId}
          AND a.EventConditionId = #{eventConditionId}
          AND a.EquipmentTemplateId = b.EquipmentTemplateId
    </select>
</mapper>