<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ControlMapper">
    <resultMap id="ControlConfigMap" type="com.siteweb.monitoring.dto.ConfigControlItem">
        <id column="equipmentTemplateId" property="equipmentTemplateId"/>
        <id column="controlId" property="controlId"/>
        <id column="stationId" property="stationId"/>
        <id column="equipmentId" property="equipmentId"/>
        <result column="resourceStructureId" property="resourceStructureId"/>
        <result column="equipmentName" property="equipmentName"/>
        <result column="controlName" property="controlName"/>
        <result column="commandType" property="commandType"/>
        <result column="unit" property="unit"/>
        <result column="displayIndex" property="displayIndex"/>
        <result column="baseTypeId" property="baseTypeId"/>
        <result column="baseTypeName" property="baseTypeName"/>
        <result column="signalId" property="signalId"/>
        <result column="maxValue" property="maxValue"/>
        <result column="minvalue" property="minValue"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="controlMeaningsList" ofType="com.siteweb.monitoring.entity.ControlMeanings"
                    column="equipmentTemplateId,controlId">
            <id column="id" property="id"/>
            <id column="equipmentTemplateId" property="equipmentTemplateId"/>
            <id column="controlId" property="controlId"/>
            <id column="parameterValue" property="parameterValue"/>
            <result column="meanings" property="meanings"/>
        </collection>

    </resultMap>
    <insert id="batchInsert">
        INSERT INTO tbl_control(equipmenttemplateid, controlid, controlname, controlcategory, cmdtoken, basetypeid,
        controlseverity, signalid, timeout, retry, description, enable, visible, displayindex, commandtype, controltype,
        datatype, "maxvalue", "minvalue", defaultvalue,ModuleNo)
        VALUES
        <foreach collection="controlList" item="control" separator=",">
            (#{control.equipmentTemplateId},#{control.controlId},#{control.controlName},#{control.controlCategory},#{control.cmdToken},#{control.baseTypeId},
            #{control.controlSeverity},#{control.signalId},#{control.timeOut},#{control.retry},#{control.description},#{control.enable},#{control.visible},#{control.displayIndex},#{control.commandType},#{control.controlType},
            #{control.dataType},#{control.maxValue},#{control.minvalue},#{control.defaultValue},#{control.moduleNo})
        </foreach>
    </insert>

    <select id="findControlsByEquipmentId" resultType="com.siteweb.monitoring.entity.Control">
        SELECT ts.id, ts.equipmenttemplateid, ts.controlid, ts.controlname, ts.controlcategory, ts.cmdtoken,
        ts.basetypeid, ts.controlseverity,ts.signalid, ts.timeout, ts.retry, ts.description, ts.enable, ts.visible,
        ts.displayindex, ts.commandtype, ts.controltype, ts.datatype, ts."maxvalue", ts."minvalue", ts.defaultvalue,
        ts.moduleno
        FROM tbl_control ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        and ts.visible = 1
        where te.EquipmentId=#{equipmentId}
    </select>
    <select id="findControlsByEquipmentTemplateId" resultType="com.siteweb.monitoring.entity.Control">
        SELECT ts.id, ts.equipmenttemplateid, ts.controlid, ts.controlname, ts.controlcategory, ts.cmdtoken,
        ts.basetypeid, ts.controlseverity,ts.signalid, ts.timeout, ts.retry, ts.description, ts.enable, ts.visible,
        ts.displayindex, ts.commandtype, ts.controltype, ts.datatype, ts."maxvalue", ts."minvalue", ts.defaultvalue,
        ts.moduleno
        FROM tbl_control ts
        where  ts.equipmenttemplateid=#{equipmentId}   and ts.visible = 1
    </select>
    <select id="findControlByStationIdEquipmentIdControlId" resultType="com.siteweb.monitoring.entity.Control">
        SELECT ts.id, ts.equipmenttemplateid, ts.controlid, ts.controlname, ts.controlcategory, ts.cmdtoken,
        ts.basetypeid, ts.controlseverity,ts.signalid, ts.timeout, ts.retry, ts.description, ts.enable, ts.visible,
        ts.displayindex, ts.commandtype, ts.controltype, ts.datatype, ts."maxvalue", ts."minvalue", ts.defaultvalue,
        ts.moduleno
        FROM tbl_control ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId=#{equipmentId} and te.StationId =#{stationId} and ts.ControlId=#{controlId}  and ts.visible = 1
    </select>
    <select id="findControlItemByEquipmentId" resultMap="ControlConfigMap">
        select c.stationId,c.resourceStructureId, c.equipmentId, c.EquipmentName,a.equipmentTemplateId, a.controlId,a.controlName, a.commandType,a.signalId, a.baseTypeId, a."minvalue",
        a."maxvalue", a.DisplayIndex,b.Id,b.parameterValue, b.meanings, b.parameterValue
        from tbl_Control a left join tbl_controlMeanings b
        on a.EquipmentTemplateId = b.EquipmentTemplateId and a.controlId = b.controlId
        inner join TBL_Equipment c on a.EquipmentTemplateId = c.EquipmentTemplateId
        where c.EquipmentId =#{equipmentId}  and a.visible = 1
    </select>

    <select id="findControlItemByEquipmentIdAndBaseTypeId" resultMap="ControlConfigMap">
        select c.stationId, c.resourceStructureId,c.equipmentId, c.EquipmentName,a.equipmentTemplateId, a.controlId,a.controlName, a.commandType,a.signalId, a.baseTypeId, a."minvalue",
        a."maxValue", a.DisplayIndex,b.Id,b.parameterValue, b.meanings, b.parameterValue
        from tbl_Control a left join tbl_controlMeanings b
        on a.EquipmentTemplateId = b.EquipmentTemplateId and a.controlId = b.controlId
        inner join TBL_Equipment c on a.EquipmentTemplateId = c.EquipmentTemplateId
        where c.EquipmentId =#{equipmentId} and a.baseTypeId = #{baseTypeId} and a.visible = 1
    </select>

    <select id="findSimpleControlDTOsByEquipmentId" resultType="com.siteweb.monitoring.dto.SimpleControlDTO">
        SELECT b.StationId, b.EquipmentId, a.ControlId, a.ControlName, a.CommandType, a."minvalue", a."maxvalue",a.baseTypeId FROM
        tbl_control a INNER JOIN TBL_Equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE b.EquipmentId = #{equipmentId} and a.visible = 1
    </select>
    <select id="findSimpleControlDTOsByEquipmentIdAndBaseTypeId"
            resultMap="ControlConfigMap">
        select c.stationId, c.resourceStructureId,c.equipmentId, c.EquipmentName,a.equipmentTemplateId, a.controlId,a.controlName, a.commandType,a.signalId, a.baseTypeId, a."minvalue",
        a."maxValue", a.DisplayIndex,b.Id,b.parameterValue, b.meanings, b.parameterValue
        from tbl_Control a left join tbl_controlMeanings b
        on a.EquipmentTemplateId = b.EquipmentTemplateId and a.controlId = b.controlId
        inner join TBL_Equipment c on a.EquipmentTemplateId = c.EquipmentTemplateId
        where c.equipmentId =#{equipmentId} and a.BaseTypeId = #{baseTypeId} and a.visible = 1
    </select>
    <select id="findSimpleControlDTOsByEquipmentIdsAndBaseTypeId"
            resultMap="ControlConfigMap">
        select c.stationId, c.resourceStructureId,c.equipmentId,c.EquipmentName, a.equipmentTemplateId, a.controlId,a.controlName, a.commandType,a.signalId, a.baseTypeId, a."minvalue",
        a."maxvalue", a.DisplayIndex,b.Id,b.parameterValue, b.meanings, b.parameterValue
        from tbl_Control a left join tbl_controlMeanings b
        on a.EquipmentTemplateId = b.EquipmentTemplateId and a.controlId = b.controlId
        inner join TBL_Equipment c on a.EquipmentTemplateId = c.EquipmentTemplateId
        where a.BaseTypeId = #{baseTypeId} and a.visible = 1
        and c.equipmentId in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>

    <select id="findSimpleControlDTOsByEquipmentIdAndControlId"
            resultMap="ControlConfigMap">
        select c.stationId, c.resourceStructureId,c.equipmentId, c.EquipmentName,a.equipmentTemplateId, a.controlId,a.controlName, a.commandType,a.signalId, a.baseTypeId, a."minvalue",
        a."maxvalue", a.DisplayIndex,b.Id,b.parameterValue, b.meanings, b.parameterValue
        from tbl_Control a left join tbl_controlMeanings b
        on a.EquipmentTemplateId = b.EquipmentTemplateId and a.controlId = b.controlId
        inner join TBL_Equipment c on a.EquipmentTemplateId = c.EquipmentTemplateId
        where c.equipmentId =#{equipmentId} and a.ControlId = #{controlId} and a.visible = 1
    </select>
    <select id="findControlInfo" resultType="com.siteweb.monitoring.dto.ControlInfoDto">
        SELECT tc.ControlName,
               tc.CmdToken,
               tc.ControlSeverity,
               tc.ControlType,
               cb.BaseTypeId,
               cb.BaseTypeName,
               tc.ControlCategory
        FROM TBL_Control tc
                 LEFT JOIN TBL_CommandBaseDic cb ON tc.BaseTypeId = cb.BaseTypeId
        WHERE tc.ControlId = #{controlId}
          AND tc.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findCommandIdByCommandCategory" resultType="java.lang.Integer">
        SELECT tc.ControlId
        FROM TBL_Control tc
        WHERE tc.EquipmentTemplateId =
              (SELECT t.EquipmentTemplateId
               FROM TBL_Equipment t
               WHERE t.StationId = #{stationId} AND t.EquipmentId = #{equipmentId})
          AND tc.ControlCategory = #{commandCategory};
    </select>
    <select id="findByEquipmentTemplateId" resultType="com.siteweb.monitoring.entity.Control">
        SELECT id,
               equipmenttemplateid,
               controlid,
               controlname,
               controlcategory,
               cmdtoken,
               basetypeid,
               controlseverity,
               signalid,
               timeout,
               retry,
               description,
               enable,
               visible,
               displayindex,
               commandtype,
               controltype,
               datatype,
               "maxvalue",
               "minvalue",
               defaultvalue,
               moduleno
        FROM tbl_control
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findSimpleControlDTOsByEquipmentIdAndCmdToken" resultMap="ControlConfigMap">
        SELECT c.stationId,
               c.resourceStructureId,
               c.equipmentId,
               c.EquipmentName,
               a.equipmentTemplateId,
               a.controlId,
               a.controlName,
               a.commandType,
               a.signalId,
               a.baseTypeId,
               a.minvalue,
               a.maxValue,
               a.DisplayIndex,
               b.Id,
               b.parameterValue,
               b.meanings,
               b.parameterValue
        FROM tbl_Control a
                 LEFT JOIN tbl_controlMeanings b
                           ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.controlId = b.controlId
                 INNER JOIN TBL_Equipment c ON a.EquipmentTemplateId = c.EquipmentTemplateId
        WHERE c.equipmentId = #{equipmentId}
          AND a.CmdToken = #{cmdToken}
          AND a.visible = 1
    </select>
</mapper>