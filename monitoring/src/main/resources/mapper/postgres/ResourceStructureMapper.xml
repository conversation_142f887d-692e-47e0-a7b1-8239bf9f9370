<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ResourceStructureMapper">
    <update id="updateEntity">
        UPDATE resourcestructure
        <set>
            <if test="resourceStructure.sceneId != null">
                SceneId = #{resourceStructure.sceneId},
            </if>
            <if test="resourceStructure.structureTypeId != null">
                StructureTypeId = #{resourceStructure.structureTypeId},
            </if>
            <if test="resourceStructure.resourceStructureName != null">
                ResourceStructureName = #{resourceStructure.resourceStructureName},
            </if>
            <if test="resourceStructure.parentResourceStructureId != null">
                ParentResourceStructureId = #{resourceStructure.parentResourceStructureId},
            </if>
            <if test="resourceStructure.photo != null">
                Photo = #{resourceStructure.photo},
            </if>
            <if test="resourceStructure.position != null">
                Position = #{resourceStructure.position},
            </if>
            <if test="resourceStructure.levelOfPath != null">
                LevelOfPath = #{resourceStructure.levelOfPath},
            </if>
            <if test="resourceStructure.display != null">
                Display = #{resourceStructure.display},
            </if>
            <if test="resourceStructure.sortValue != null">
                SortValue = #{resourceStructure.sortValue},
            </if>
            <if test="resourceStructure.extendedField != null">
                ExtendedField = #{resourceStructure.extendedField}::json,
            </if>
            <if test="resourceStructure.originId != null">
                OriginId = #{resourceStructure.originId},
            </if>
            <if test="resourceStructure.originParentId != null">
                OriginParentId = #{resourceStructure.originParentId},
            </if>
        </set>
        WHERE ResourceStructureId = #{resourceStructure.resourceStructureId}
    </update>
    <update id="updateBatchOrder">
        <foreach collection="list" item="item" separator=";">
            UPDATE resourcestructure SET SortValue = #{item.sortValue}
            WHERE ResourceStructureId = #{item.resourceStructureId}
        </foreach>
    </update>

    <select id="findSubResourceStructureByEquipmentCategory"
            resultType="com.siteweb.monitoring.entity.ResourceStructure">
        select a.*
        from resourcestructure  b
        inner join resourcestructure a on a.LevelOfPath like  CONCAT(b.LevelOfPath , '%')
        and b.resourcestructureId = #{rootId}
        where exists(select 1 from tbl_equipment c  inner join tbl_equipmentTemplate d on c.EquipmentTemplateId = d.EquipmentTemplateId
        where a.resourcestructureId = c.resourcestructureId and  d.EquipmentBaseType =#{equipmentCategory} )
    </select>
    <select id="getResourceStructureAlarmState"
            resultType="com.siteweb.monitoring.dto.ResourceStructureAlarmState">
        select c.ResourceStructureId,c.ResourceStructureName, c.StructureTypeId,min(b.EventLevel) AS EventLevel from tbl_activeevent b
        inner join resourcestructure a on a.ResourceStructureId = b.ResourceStructureId
        inner join resourcestructure c on  a.LevelOfPath like CONCAT(c.LevelOfPath , '%')
        where b.EndTime is null and  c.ResourceStructureId  in
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
        group by c.ResourceStructureId,c.ResourceStructureName, c.StructureTypeId
    </select>
</mapper>