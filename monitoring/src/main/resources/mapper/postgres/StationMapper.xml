<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.StationMapper">
    <resultMap id="sationStateMap" type="com.siteweb.monitoring.model.StationState">
        <id column="StationId" property="stationId"/>
        <id column="ConnectState" property="onlineState"  typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <id column="onProject" property="onProject" />
        <id column="masked" property="masked" />
    </resultMap>

    <update id="setStationState">
        Update TBL_Station set StartTime = #{startTime}, EndTime = #{endTime},StationState = #{stationState} where StationId = #{stationId}
    </update>
    <update id="saveProjectStateStation">
        <choose>
            <when test="_databaseId == 'opengauss'">
                INSERT INTO TBL_ProjectStateStation (StationId, Reason, StartTime, EndTime, UserId, LastUpdateDate)
                VALUES (#{stationId}, #{reason}, #{startTime}, #{endTime}, #{userId}, now())
                ON DUPLICATE KEY UPDATE Reason = VALUES (Reason),
                StartTime = VALUES(StartTime),
                EndTime = VALUES (EndTime),
                UserId = VALUES(UserId),
                LastUpdateDate = now();
            </when>
            <otherwise>
                INSERT INTO TBL_ProjectStateStation (StationId, Reason, StartTime, EndTime, UserId, LastUpdateDate)
                VALUES (#{stationId}, #{reason}, #{startTime}, #{endTime}, #{userId}, now())
                ON CONFLICT (StationId) DO UPDATE SET
                Reason = EXCLUDED.Reason,
                StartTime = EXCLUDED.StartTime,
                EndTime = EXCLUDED.EndTime,
                UserId = EXCLUDED.UserId,
                LastUpdateDate = now();
            </otherwise>
        </choose>
    </update>
    <select id="getStationsInBatch" resultType="com.siteweb.monitoring.entity.Station">
        SELECT StationId, StationName, Latitude, Longitude, SetupTime, CompanyId, ConnectState, UpdateTime, StationCategory, StationGrade, StationState, ContactId, SupportTime, OnWayTime, SurplusTime, FloorNo, PropList, Acreage, BuildingType, ContainNode, Description, BordNumber, CenterId, Enable, StartTime, EndTime, ProjectName, ContractNo, InstallTime
        FROM siteweb.tbl_station a
        where a.StationId  IN
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </select>
    <select id="getStationProjectVO" resultType="com.siteweb.monitoring.vo.StationProjectVO">
        SELECT StationId, StartTime, EndTime, Reason from  TBL_ProjectStateStation WHERE StationId = #{stationId}
    </select>
    <select id="getAllProjectStations" resultType="com.siteweb.monitoring.dto.StationProjectDetail">
        SELECT b.StationId, b.stationName, c.ItemValue AS StationCategory, a.StartTime, a.EndTime, a.Reason,a.UserId from  TBL_ProjectStateStation  a
        inner join tbl_Station b on a.StationId = b.StationId
        inner join tbl_dataItem c on b.StationCategory= c.ItemId and c.entryId=71
        where a.StartTime &lt;= now() and a.EndTime &gt;= now();
    </select>
    <select id="getStationGroupStateDTO" resultType="com.siteweb.monitoring.dto.StationGroupStateDTO">
        select count(distinct a.StationId) AS totalCount,  count(distinct b.StationId) AS alarmCount,
        sum(case when a.ConnectState = 1 then 1 else 0 end) AS onlinecount,
        sum(case when a.ConnectState != 1 then 1 else 0 end) AS offlinecount
        from tbl_station a left join (
        select stationId, count(*) alarmcount from tbl_activeevent
        group by stationId) b on a.StationId = b.StationId
        where a.StationId  IN
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </select>
    <select id="getAllByStationStates"  resultMap="sationStateMap">
        update tbl_station set StationState = 1;
        update tbl_station set StationState=3 where StartTime &lt; now() and EndTime > now() ;
        select a.StationId, a.ConnectState,
        case when a.StartTime &lt; now() AND a.EndTime &gt; now() then 1 else 0 end AS onProject,
        case when c.StartTime &lt;  now() AND c.EndTime &gt; now() then 1 else 0 end AS masked
        from tbl_station a Left join tbl_stationmask c on a.StationId = c.StationId;
    </select>

    <select id="findStationIdsByUserId" resultType="java.lang.Integer">
            SELECT distinct su.StationId
            FROM   TBL_Account            ul
                       INNER JOIN  TBL_UserRoleMap        mp   ON ul.UserId       =   mp.UserId
                       INNER JOIN  TBL_UserRole           ur   ON mp.RoleId       =   ur.RoleId
                       INNER JOIN  TBL_UserRoleRight      urr  ON ur.RoleId       =   urr.RoleId        AND urr.OperationType = 2
                       INNER JOIN  TBL_Area               a    ON a.AreaId        =   urr.OperationId
                       INNER JOIN  TBL_AreaMap            am   ON a.AreaId        =   am.AreaId
                       INNER JOIN  TBL_Station			   su   ON am.StationId    =   su.StationId
                       INNER JOIN  TBL_StationStructureMap sp  ON su.StationId	   =   sp.StationId
            WHERE ul.UserId = #{userId}
    </select>
    <select id="findStationBaseTypeByStationIdAndStandardVer" resultType="java.lang.Integer">
        SELECT stationBase.StationBaseType
        FROM TBL_Station station,
             TBL_StationBaseMap stationBase
        WHERE station.StationCategory = stationBase.StationCategory
          AND station.StationId = #{stationId}
          AND stationBase.StandardType = #{standardVer};
    </select>
    <select id="findIdsByFilterCondition" resultType="java.lang.Integer">
        SELECT a.StationId FROM tbl_station a INNER JOIN tbl_stationstructuremap b ON a.StationId = b.StationId
        INNER JOIN tbl_stationstructure c on b.StructureId = c.StructureId
        <where>
            <if test="stationFilterDTO.stationIdList != null and stationFilterDTO.stationIdList.size > 0">
                AND a.StationId IN
                <foreach collection="stationFilterDTO.stationIdList" item="stationId" open="(" close=")" separator=",">
                    #{stationId}
                </foreach>
            </if>
            <if test="stationFilterDTO.stationStateList != null and stationFilterDTO.stationStateList.size > 0">
                AND a.StationState IN
                <foreach collection="stationFilterDTO.stationStateList" item="stationState" open="(" close=")" separator=",">
                    #{stationState}
                </foreach>
            </if>
            <if test="stationFilterDTO.stationCategoryList != null and stationFilterDTO.stationCategoryList.size > 0">
                AND a.StationCategory IN
                <foreach collection="stationFilterDTO.stationCategoryList" item="stationCategory" open="(" close=")" separator=",">
                    #{stationCategory}
                </foreach>
            </if>
            <if test="stationFilterDTO.stationStructureList != null and stationFilterDTO.stationStructureList.size > 0">
                AND c.StructureId IN
                <foreach collection="stationFilterDTO.stationStructureList" item="stationStructure" open="(" close=")" separator=",">
                    #{stationStructure}
                </foreach>
            </if>
            <if test="stationFilterDTO.stationGroupType != null">
                AND c.StructureGroupId = #{stationFilterDTO.stationGroupType}
            </if>
        </where>
    </select>
</mapper>