<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.AlarmMaskLogMapper">


    <sql id="Base_Column_List">
        Id, StationId, EquipmentId, EventId, ResourceStructureId, UserId, OperationType, OperationTime, StartTime, EndTime, Comment
    </sql>
    <insert id="insertEntity">
        INSERT INTO alarmmasklog (StationId, EquipmentId, EventId, ResourceStructureId, UserId, OperationType,
                                  OperationTime, TimeGroupCategory, StartTime, EndTime, TimeGroupChars, Comment)
        VALUES (#{alarmMaskLog.stationId}, #{alarmMaskLog.equipmentId}, #{alarmMaskLog.eventId},
                #{alarmMaskLog.resourceStructureId}, #{alarmMaskLog.userId}, #{alarmMaskLog.operationType},
                #{alarmMaskLog.operationTime}, #{alarmMaskLog.timeGroupCategory}, #{alarmMaskLog.startTime},
                #{alarmMaskLog.endTime}, #{alarmMaskLog.timeGroupChars}, #{alarmMaskLog.comment});
    </insert>
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO alarmmasklog (
        StationId,
        EquipmentId,
        EventId,
        ResourceStructureId,
        UserId,
        OperationType,
        OperationTime,
        TimeGroupCategory,
        StartTime,
        EndTime,
        TimeGroupChars,
        Comment
        ) VALUES
        <foreach collection="alarmMaskLogList" item="item" separator=",">
            (
            #{item.stationId},
            #{item.equipmentId},
            #{item.eventId},
            #{item.resourceStructureId},
            #{item.userId},
            #{item.operationType},
            #{item.operationTime},
            #{item.timeGroupCategory},
            #{item.startTime},
            #{item.endTime},
            #{item.timeGroupChars},
            #{item.comment}
            )
        </foreach>
    </insert>

    <resultMap id="alarmMaskLogDTO" type="com.siteweb.monitoring.dto.AlarmMaskLogDTO">
        <result property="stationId" column="stationId"/>
        <result property="stationName" column="stationName"/>
        <result property="equipmentId" column="equipmentId"/>
        <result property="eventId" column="eventId"/>
        <result property="eventName" column="eventName"/>
        <result property="resourceStructureId" column="resourceStructureId"/>
        <result property="userId" column="userId"/>
        <result property="userName" column="userName"/>
        <result property="operationType" column="operationType"/>
        <result property="operationTime" column="operationTime"/>
        <result property="startTime" column="startTime"/>
        <result property="endTime" column="endTime"/>
        <result property="comment" column="comment"/>
    </resultMap>


    <select id="findAlarmMaskLogDTO" resultMap="alarmMaskLogDTO">
        select t1.*, ts.StationName, ta.UserName, tae.EventName from (
            select <include refid="Base_Column_List"/> from alarmmasklog t1
            ${ew.customSqlSegment}
        ) t1
        left join tbl_activeevent tae on tae.StationId = t1.stationId and tae.EquipmentId = t1.equipmentId and tae.EventId = t1.eventId
        left join tbl_station ts on t1.StationId = ts.StationId
        left join tbl_account ta on ta.UserId = t1.UserId
    </select>
    <select id="findAlarmMaskLog" resultType="com.siteweb.monitoring.dto.AlarmMaskLogDTO">
        select t1.Id, t1.StationId, t1.EquipmentId, t1.EventId, t1.ResourceStructureId,
        t1.UserId, t1.OperationType, t1.OperationTime, t1.StartTime, t1.EndTime,
        t1.Comment, t1.TimeGroupCategory, t1.timeGroupChars, ta.UserName, tae.EventName,
        fuct_GetDevicePosition(t1.ResourceStructureId) as ResourceStructureName
        from alarmmasklog t1
        left join tbl_equipment te on te.EquipmentId = t1.EquipmentId
        left join tbl_event tae on tae.EquipmentTemplateId = te.EquipmentTemplateId and tae.EventId = t1.EventId
        left join tbl_account ta on ta.UserId = t1.UserId
        <where>
            <if test="param.equipmentIds != null and param.equipmentIds.size > 0">
                AND te.EquipmentId IN
                <foreach collection="param.equipmentIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.operatorIds != null and param.operatorIds.size > 0">
                AND t1.UserId IN
                <foreach collection="param.operatorIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="param.operationType != null">
                AND t1.OperationType = #{param.operationType}
            </if>
            <if test="param.maskStartDate != null">
                AND t1.StartTime &gt;= #{param.maskStartDate}
            </if>
            <if test="param.maskEndDate != null">
                AND t1.StartTime &lt;= #{param.maskEndDate}
            </if>
            <if test="param.startDate != null">
                AND t1.OperationTime &gt;= #{param.startDate}
            </if>
            <if test="param.endDate != null">
                AND t1.OperationTime &lt;= #{param.endDate}
            </if>
            <if test="param.eventIds != null and !param.eventIds.isEmpty()">
                AND
                <foreach collection="param.eventIds" item="eventIds" index="equipmentId" open="(" close=")" separator=" or ">
                    <foreach collection="eventIds" item="eventId" separator=" or ">
                        (te.EquipmentId = #{equipmentId} and tae.EventId = #{eventId})
                    </foreach>
                </foreach>
            </if>
        </where>
        order by OperationTime desc
    </select>
</mapper>