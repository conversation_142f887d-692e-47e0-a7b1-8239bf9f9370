<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.SignalMapper">
    <resultMap id="SignalConfigMap" type="com.siteweb.monitoring.dto.ConfigSignalItem">
        <id column="equipmentTemplateId" property="equipmentTemplateId"/>
        <id column="signalId" property="signalId"/>
        <result column="signalName" property="signalName"/>
        <result column="signalCategory" property="signalCategory"/>
        <result column="unit" property="unit"/>
        <result column="displayIndex" property="displayIndex"/>
        <result column="showPrecision" property="showPrecision"/>
        <result column="baseTypeId" property="baseTypeId"/>
        <result column="visible" property="visible"/>
        <result column="properties" property="properties"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="meaningsList" ofType="com.siteweb.monitoring.entity.SignalMeanings"
                    column="equipmentTemplateId,signalId">
            <id column="meaningId" property="id"/>
            <result column="equipmentTemplateId" property="equipmentTemplateId"/>
            <result column="signalId" property="signalId"/>
            <result column="stateValue" property="stateValue"/>
            <result column="meanings" property="meanings"/>
        </collection>
    </resultMap>
    <sql id="signalField">
        id, equipmenttemplateid, signalid, enable, visible, description, signalname, signalcategory, signaltype,
        channelno, channeltype, expression, datatype, showprecision, unit, storeinterval, absvaluethreshold,
        percentthreshold, staticsperiod, basetypeid, chargestoreinterval, chargeabsvalue, displayindex, mdbsignalid,
        moduleno
    </sql>
    <insert id="batchInsert">
        INSERT INTO tbl_signal(EquipmentTemplateId, SignalId, Enable, Visible, Description, SignalName, SignalCategory,
        SignalType, ChannelNo, ChannelType, Expression, DataType, ShowPrecision, Unit,
        StoreInterval, AbsvalueThreshold, PercentThreshold, StaticsPeriod, BaseTypeId,
        ChargeStoreInterval, ChargeAbsValue, DisplayIndex, MDBSignalId, ModuleNo)
        VALUES
        <foreach collection="signalList" item="item" separator=",">
            (#{item.equipmentTemplateId},#{item.signalId},#{item.enable},#{item.visible},#{item.description},#{item.signalName},#{item.signalCategory},
             #{item.signalType},#{item.channelNo},#{item.channelType},#{item.expression},#{item.dataType},#{item.showPrecision},#{item.unit},
             #{item.storeInterval},#{item.absValueThreshold},#{item.percentThreshold},#{item.staticsPeriod},#{item.baseTypeId},
             #{item.chargeStoreInterval},#{item.chargeAbsValue},#{item.displayIndex},#{item.mdbSignalId},#{item.moduleNo})
        </foreach>
    </insert>
    <update id="updateEntity">
        UPDATE TBL_Signal  SET SignalName = #{signal.signalName},Expression = #{signal.expression}, ShowPrecision = #{signal.showPrecision},
                                StoreInterval = #{signal.storeInterval},AbsValueThreshold = #{signal.absValueThreshold}, PercentThreshold = #{signal.percentThreshold},
                                StaticsPeriod = #{signal.staticsPeriod}
        WHERE EquipmentTemplateId=#{signal.equipmentTemplateId} AND SignalId = #{signal.signalId}
    </update>

    <select id="findConfigSignalByEquipmentTemplateId" resultMap="SignalConfigMap">
        select a.equipmentTemplateId, a.signalId,a.SignalName, a.signalCategory,a.unit, a.baseTypeId, a.showPrecision,
        a.DisplayIndex,c.Id AS MeaningId, c.stateValue, c.meanings, a.Visible,b.properties
        from tbl_signal a left join tbl_signalMeanings c
        on a.EquipmentTemplateId = c.EquipmentTemplateId and a.SignalId = c.SignalId
        left join  (select equipmentTemplateId, signalId,STRING_AGG(signalPropertyId::text, '/')  as properties from tbl_SignalProperty
        where EquipmentTemplateId =#{equipmentTemplateId}
        group by equipmentTemplateId, signalId ) b on a.EquipmentTemplateId = b.EquipmentTemplateId and a.SignalId = b.SignalId
        where a.EquipmentTemplateId =#{equipmentTemplateId}
    </select>
    <select id="findAllConfigSignals" resultMap="SignalConfigMap">
        select a.equipmentTemplateId, a.signalId,a.SignalName, a.signalCategory,a.unit, a.baseTypeId, a.showPrecision,
               a.DisplayIndex,c.Id AS MeaningId, c.stateValue, c.meanings, a.Visible,b.properties
        from tbl_signal a left join tbl_signalMeanings c
                                    on a.EquipmentTemplateId = c.EquipmentTemplateId and a.SignalId = c.SignalId
                          left join  (select equipmentTemplateId, signalId,STRING_AGG(signalPropertyId::text, '/') as properties from tbl_SignalProperty
                                      group by equipmentTemplateId, signalId) b on a.EquipmentTemplateId = b.EquipmentTemplateId and a.SignalId = b.SignalId
    </select>

    <select id="findSignalsByEquipmentId" resultType="com.siteweb.monitoring.entity.Signal">
        SELECT ts.id, ts.equipmenttemplateid, ts.signalid, ts.enable, ts.visible, ts.description, ts.signalname,
        ts.signalcategory, ts.signaltype, ts.channelno, ts.channeltype, ts.expression, ts.datatype, ts.showprecision,
        ts.unit, ts.storeinterval, ts.absvaluethreshold, ts.percentthreshold, ts.staticsperiod, ts.basetypeid,
        ts.chargestoreinterval, ts.chargeabsvalue, ts.displayindex, ts.mdbsignalid, ts.moduleno
        FROM TBL_Signal ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId=#{equipmentId}
    </select>

    <select id="findSignalsByEquipmentTemplateId" resultType="com.siteweb.monitoring.entity.Signal">
        SELECT ts.id, ts.equipmenttemplateid, ts.signalid, ts.enable, ts.visible, ts.description, ts.signalname,
        ts.signalcategory, ts.signaltype, ts.channelno, ts.channeltype, ts.expression, ts.datatype, ts.showprecision,
        ts.unit, ts.storeinterval, ts.absvaluethreshold, ts.percentthreshold, ts.staticsperiod, ts.basetypeid,
        ts.chargestoreinterval, ts.chargeabsvalue, ts.displayindex, ts.mdbsignalid, ts.moduleno
        FROM TBL_Signal ts
        where ts.equipmenttemplateid=#{equipmentId}
    </select>

    <select id="findSignalsAboutControlByEquipmentId" resultType="com.siteweb.monitoring.model.ControlSignalRelation">
        SELECT ts.equipmenttemplateid, ts.signalid, ts.signalname, ts.unit AS signalunit, tc.controlId
        FROM TBL_Signal ts inner join TBL_Control tc on ts.EquipmentTemplateId = tc.EquipmentTemplateId and ts.SignalId
        = ts.SignalId
        inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId=#{equipmentId}
    </select>

    <select id="findRealTimeSignalKeyByEquipmentId" resultType="java.lang.String">
        SELECT CONCAT('1.', te.EquipmentId, '.', ts.SignalId) from TBL_Signal ts inner join TBL_Equipment te on
        ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId = #{equipmentId}
    </select>

    <select id="findRealTimeSignalKeyByBaseTypeId" resultType="java.lang.String">
        SELECT CONCAT('1.', te.EquipmentId, '.', ts.SignalId) from TBL_Signal ts inner join TBL_Equipment te on
        ts.EquipmentTemplateId = te.EquipmentTemplateId
        where ts.BaseTypeId = #{baseTypeId} and te.EquipmentBaseType ={baseTypeId}/1000000
    </select>

    <select id="findRealTimeSignalKeyByEquipmentCategoryAndBaseTypeId" resultType="java.lang.String">
        SELECT CONCAT('1.', te.EquipmentId, '.', ts.SignalId) from TBL_Signal ts inner join TBL_Equipment te on
        ts.EquipmentTemplateId = te.EquipmentTemplateId
        where ts.BaseTypeId in #{baseTypeId} and te.EquipmentBaseType = equipmentBaseTypeId
    </select>
    <select id="findSimpleSignalDTOsByEquipmentId" resultType="com.siteweb.monitoring.dto.SimpleSignalDTO">
        SELECT b.StationId, b.EquipmentId, a.SignalId, a.SignalName FROM tbl_signal a INNER JOIN TBL_Equipment b
        ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE b.EquipmentId = #{equipmentId}
    </select>
    <select id="findConfigSignalDTOBySignalId" resultType="com.siteweb.monitoring.dto.ConfigSignalDTO">
        SELECT * FROM TBL_Signal a, TBL_Equipment b
        WHERE b.EquipmentTemplateId = a.EquipmentTemplateId
        AND a.SignalId = #{signalId}
        AND b.EquipmentId = #{equipmentId}
    </select>
    <select id="findMonitorUnitExpressionBySignalId" resultType="java.lang.String">
        SELECT Expression FROM TSL_MonitorUnitSignal
        WHERE InstanceType = 2 AND EquipmentId = #{equipmentId} AND SignalId = #{signalId}
    </select>

    <parameterMap id="saveSignalTemplateParamMap" type="java.util.HashMap">
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="SourceTemplateId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="DestTemplateId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="SignalId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="SignalName" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="Expression" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="ShowPrecision" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="StoreInterval" mode="IN" jdbcType="DOUBLE"></parameter>
        <parameter property="AbsValueThreshold" mode="IN" jdbcType="DOUBLE"></parameter>
        <parameter property="PercentThreshold" mode="IN" jdbcType="DOUBLE"></parameter>
        <parameter property="StaticsPeriod" mode="IN" jdbcType="INTEGER"></parameter>
    </parameterMap>
    <select id="findMaxSignalIdByTemplateId" resultType="java.lang.Integer">
        SELECT max(SignalId) FROM tbl_signal WHERE EquipmentTemplateId = #{templateId}
    </select>
    <select id="findSignalsByTemplateIdAndSignalIds" resultType="com.siteweb.monitoring.entity.Signal">
        SELECT <include refid="signalField"/> FROM tbl_signal WHERE EquipmentTemplateId = #{templateId} AND
        SignalId IN
        <foreach collection="signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findSignalIdByTemplateIdAndSignalName" resultType="com.siteweb.monitoring.entity.Signal">
        SELECT <include refid="signalField"/>
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
          AND signalName = #{virtualSignalName}
        LIMIT 1
    </select>
    <select id="findSignalIdByTemplateIdAndChannel" resultType="com.siteweb.monitoring.entity.Signal">
        SELECT <include refid="signalField"/>
        FROM tbl_signal
        WHERE EquipmentTemplateId = #{equipmentTemplateId}
        AND channelNo = #{channelNo}
        LIMIT 1
    </select>
    <select id="findSignalEvent" resultType="com.siteweb.monitoring.dto.SimpleEventSignalDTO">
        SELECT b.signalId,
               b.SignalName,
               c.EventId,
               c.EventName,
               a.EquipmentId
        FROM TBL_Equipment a
                 LEFT JOIN tbl_signal b ON b.EquipmentTemplateId = a.EquipmentTemplateId
                 LEFT JOIN tbl_event c ON c.EquipmentTemplateId = a.EquipmentTemplateid AND b.SignalId = c.SignalId
        WHERE a.EquipmentId = #{equipmentId}
        ORDER BY b.signalId
    </select>
    <select id="findSignalsByEquipmentIdAndSignalIds" resultType="com.siteweb.monitoring.dto.EquipmentSignalDto">
        SELECT ts.id, ts.equipmenttemplateid, ts.signalid, ts.enable, ts.visible, ts.description, ts.signalname,
               ts.signalcategory, ts.signaltype, ts.channelno, ts.channeltype, ts.expression, ts.datatype, ts.showprecision,
               ts.unit, ts.storeinterval, ts.absvaluethreshold, ts.percentthreshold, ts.staticsperiod, ts.basetypeid,
               ts.chargestoreinterval, ts.chargeabsvalue, ts.displayindex, ts.mdbsignalid, ts.moduleno,te.EquipmentId
        FROM TBL_Signal ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId=#{equipmentId} AND
        ts.SignalId IN
        <foreach collection="signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findSimpleSignalDTOsByBaseTypeIds" resultType="com.siteweb.monitoring.dto.SimpleSignalDTO">
        SELECT b.StationId, b.EquipmentId, c.SignalId, c.SignalName
        FROM tbl_Equipment b
        INNER JOIN tbl_Signal c ON b.EquipmentTemplateId = c.EquipmentTemplateId
        WHERE c.BaseTypeId IN
        <foreach collection="baseTypeIdList" item="baseTypeId" open="(" close=")" separator=",">
            #{baseTypeId}
        </foreach>
        <if test="equipmentIds != null and !equipmentIds.isEmpty()">
            AND b.EquipmentId IN
            <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </if>
    </select>
</mapper>