<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EquipmentMaskMapper">

    <parameterMap id="opSaveEquipMaskMap" type="java.util.HashMap">
        <parameter property="StationId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="TimeGroupId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="StartTime" mode="IN" jdbcType="TIMESTAMP"></parameter>
        <parameter property="EndTime" mode="IN" jdbcType="TIMESTAMP"></parameter>
        <parameter property="UserId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="Reason" mode="IN" jdbcType="VARCHAR"></parameter>
    </parameterMap>
    <insert id="batchInsert">
        INSERT INTO tbl_equipmentmask (
        StationId,
        EquipmentId,
        TimeGroupId,
        Reason,
        StartTime,
        EndTime,
        UserId
        ) VALUES
        <foreach collection="insertList" item="item" index="index" separator=",">
            (
            #{item.stationId},
            #{item.equipmentId},
            #{item.timeGroupId},
            #{item.reason},
            #{item.startTime},
            #{item.endTime},
            #{item.userId}
            )
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="updateList" item="item" index="index" separator=";">
            UPDATE tbl_equipmentmask
            SET
            TimeGroupId = #{item.timeGroupId},
            Reason = #{item.reason},
            StartTime = #{item.startTime},
            EndTime = #{item.endTime},
            UserId = #{item.userId}
            WHERE
            StationId = #{item.stationId}
            AND EquipmentId = #{item.equipmentId}
        </foreach>
    </update>
    <delete id="deleteEquipmentMask">
        DELETE FROM TBL_EquipmentMask WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId};
        DELETE FROM TBL_TimeGroupSpan WHERE TimeGroupId = #{timeGroupId};
    </delete>

    <parameterMap id="opSaveSeparateEquipmentMaskMap" type="java.util.HashMap">
        <parameter property="StationId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="TimeGroupId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="TimeMaskChar" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="Week" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="UserId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="Reason" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="ret" mode="OUT" jdbcType="INTEGER"></parameter>
    </parameterMap>

    <select id="findSimpleEquipmentMaskDTOsByResourceStructureId"
            resultType="com.siteweb.monitoring.dto.SimpleEquipmentMaskDTO">
        SELECT a.StationId, a.EquipmentId, a.EquipmentName, CASE WHEN b.EquipmentId IS NOT NULL THEN 1 ELSE 0 END AS Mask
        FROM tbl_equipment a LEFT JOIN tbl_equipmentmask b ON a.StationId = b.StationId and a.EquipmentId = b.EquipmentId
        INNER JOIN resourcestructure c ON a.ResourceStructureId = c.ResourceStructureId
        WHERE    c.LevelOfPath LIKE CONCAT((select LevelOfPath from resourcestructure where ResourceStructureId = #{resourceStructureId}),'%')
    </select>
    <select id="findEquipmentMaskDTOByEquipmentId" resultType="com.siteweb.monitoring.dto.EquipmentMaskDTO">
        SELECT a.*,b.UserName,c.EquipmentName,fuct_GetDevicePosition(c.ResourceStructureId) AS EquipmentPosition
        FROM tbl_equipmentmask a LEFT JOIN tbl_account b ON a.userId = b.userId
        LEFT JOIN tbl_equipment c ON a.StationId = c.StationId AND a.EquipmentId = c.EquipmentId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="findEquipmentMasksByResourceStructureId"
            resultType="com.siteweb.monitoring.entity.EquipmentMask">
        SELECT a.*
        FROM tbl_equipmentmask a INNER JOIN tbl_equipment b ON a.StationId = b.StationId and a.EquipmentId = b.EquipmentId
        INNER JOIN resourcestructure c ON b.ResourceStructureId = c.ResourceStructureId
        WHERE c.LevelOfPath LIKE CONCAT((select LevelOfPath from resourcestructure where ResourceStructureId = #{resourceStructureId}),'%')
    </select>
    <select id="findMaskEquipmentIds" resultType="java.lang.Integer">
        SELECT EquipmentId
        FROM TBL_EquipmentMask
        WHERE StartTime &lt;= NOW()
          AND EndTime &gt;= NOW();
    </select>
    <sql id="findEquipmentMaskByKeywordsPageSql">
        SELECT a.StationId,
        a.EquipmentId,
        a.TimeGroupId,
        a.Reason,
        a.StartTime,
        a.EndTime,
        a.UserId,
        b.UserName,
        c.EquipmentName,
        fuct_GetDevicePosition(c.ResourceStructureId) AS EquipmentPosition
        FROM tbl_equipmentmask a
        LEFT JOIN tbl_account b ON a.userId = b.userId
        LEFT JOIN tbl_equipment c ON a.StationId = c.StationId AND a.EquipmentId = c.EquipmentId
        <where>
            <if test="equipmentMaskFilterDTO.keywords != null and equipmentMaskFilterDTO.keywords != ''">
                AND ((c.EquipmentName LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(c.ResourceStructureId) LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')))
            </if>
            AND a.equipmentId IN
            <foreach collection="equipmentMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </where>
        ORDER BY
        <choose>
            <when test="equipmentMaskFilterDTO.field != null and equipmentMaskFilterDTO.field != ''">
                ${equipmentMaskFilterDTO.field}
                <if test="equipmentMaskFilterDTO.order != null and equipmentMaskFilterDTO.order != ''">
                    ${equipmentMaskFilterDTO.order}
                </if>
            </when>
            <otherwise>
                a.StartTime desc
            </otherwise>
        </choose>
    </sql>
    <select id="findEquipmentMaskByKeywordsPage" resultType="com.siteweb.monitoring.dto.EquipmentMaskDTO">
       <include refid="findEquipmentMaskByKeywordsPageSql"/>
    </select>
    <select id="findEquipmentMaskByKeywordsPageCount" resultType="java.lang.Long">
        SELECT count(*) FROM (<include refid="findEquipmentMaskByKeywordsPageSql"/>) as countTable
    </select>
    <sql id="findEquipmentMaskByEquipmentBaseTypesPageSql">
        SELECT a.StationId, a.EquipmentId, a.EquipmentName, CASE WHEN b.EquipmentId IS NOT NULL THEN 1 ELSE 0 END AS Mask
        FROM tbl_equipment a LEFT JOIN tbl_equipmentmask b ON a.StationId = b.StationId and a.EquipmentId = b.EquipmentId
        INNER JOIN resourcestructure c ON a.ResourceStructureId = c.ResourceStructureId
        LEFT JOIN tbl_equipmenttemplate d on d.EquipmentTemplateId = a.EquipmentTemplateId
        <where>
            <if test="equipmentMaskFilterDTO.resourceStructureIdList != null and equipmentMaskFilterDTO.resourceStructureIdList.size >0">
                AND c.ResourceStructureId IN
                <foreach collection="equipmentMaskFilterDTO.resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="equipmentMaskFilterDTO.equipmentBaseTypeIdList != null and equipmentMaskFilterDTO.equipmentBaseTypeIdList.size > 0">
                AND d.EquipmentBaseType IN
                <foreach collection="equipmentMaskFilterDTO.equipmentBaseTypeIdList" item="equipmentBaseType" open="(" close=")" separator=",">
                    #{equipmentBaseType}
                </foreach>
            </if>
            <if test="equipmentMaskFilterDTO.equipmentCategoryIdList != null and equipmentMaskFilterDTO.equipmentCategoryIdList.size > 0">
                AND a.EquipmentCategory IN
                <foreach collection="equipmentMaskFilterDTO.equipmentCategoryIdList" item="equipmentCategoryId" open="(" close=")" separator=",">
                    #{equipmentCategoryId}
                </foreach>
            </if>
            AND a.equipmentId IN
            <foreach collection="equipmentMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
            <if test="equipmentMaskFilterDTO.keywords != null and equipmentMaskFilterDTO.keywords != ''">
                AND ((a.EquipmentName LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(c.ResourceStructureId) LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')))
            </if>
        </where>
        order by
        <choose>
            <when test="equipmentMaskFilterDTO.field != null and equipmentMaskFilterDTO.field != ''">
                ${equipmentMaskFilterDTO.field}
                <if test="equipmentMaskFilterDTO.order != null and equipmentMaskFilterDTO.order != ''">
                    ${equipmentMaskFilterDTO.order}
                </if>
            </when>
            <otherwise>
                a.StartTime desc
            </otherwise>
        </choose>
    </sql>
    <select id="findSimpleEquipmentMaskByEquipmentBaseTypesPage" resultType="com.siteweb.monitoring.dto.SimpleEquipmentMaskDTO">
        <include refid="findEquipmentMaskByEquipmentBaseTypesPageSql"/>
    </select>
    <select id="findEquipmentMaskByEquipmentBaseTypesPageCount" resultType="java.lang.Long">
        SELECT count(*) FROM (<include refid="findEquipmentMaskByEquipmentBaseTypesPageSql"/>) countTable
    </select>
    <select id="findEquipmentMaskByEquipmentBaseTypes" resultType="com.siteweb.monitoring.entity.EquipmentMask">
        SELECT a.EquipmentId, a.StationId, b.TimeGroupId, b.Reason, b.StartTime, b.EndTime, b.UserId
        FROM tbl_equipment a INNER JOIN tbl_equipmentmask b ON a.StationId = b.StationId and a.EquipmentId = b.EquipmentId
        INNER JOIN resourcestructure c ON a.ResourceStructureId = c.ResourceStructureId
        LEFT JOIN tbl_equipmenttemplate d on d.EquipmentTemplateId = a.EquipmentTemplateId
        <where>
            <if test="equipmentMaskFilterDTO.keywords != null and equipmentMaskFilterDTO.keywords != ''">
                AND ((a.EquipmentName LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(c.ResourceStructureId) LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')))
            </if>
            <if test="equipmentMaskFilterDTO.resourceStructureIdList != null and equipmentMaskFilterDTO.resourceStructureIdList.size >0">
                AND c.ResourceStructureId IN
                <foreach collection="equipmentMaskFilterDTO.resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="equipmentMaskFilterDTO.equipmentBaseTypeIdList != null and equipmentMaskFilterDTO.equipmentBaseTypeIdList.size > 0">
                AND d.EquipmentBaseType IN
                <foreach collection="equipmentMaskFilterDTO.equipmentBaseTypeIdList" item="equipmentBaseType" open="(" close=")" separator=",">
                    #{equipmentBaseType}
                </foreach>
            </if>
            <if test="equipmentMaskFilterDTO.equipmentCategoryIdList != null and equipmentMaskFilterDTO.equipmentCategoryIdList.size > 0">
                AND a.EquipmentCategory IN
                <foreach collection="equipmentMaskFilterDTO.equipmentCategoryIdList" item="equipmentCategoryId" open="(" close=")" separator=",">
                    #{equipmentCategoryId}
                </foreach>
            </if>
            AND a.equipmentId IN
            <foreach collection="equipmentMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </where>
    </select>
    <select id="findEquipmentMaskCreateByEquipmentBaseTypes" resultType="com.siteweb.monitoring.entity.EquipmentMask">
        SELECT a.EquipmentId, a.StationId
        FROM tbl_equipment a
        INNER JOIN resourcestructure c ON a.ResourceStructureId = c.ResourceStructureId
        LEFT JOIN tbl_equipmenttemplate d on d.EquipmentTemplateId = a.EquipmentTemplateId
        <where>
            <if test="equipmentMaskFilterDTO.keywords != null and equipmentMaskFilterDTO.keywords != ''">
                AND ((a.EquipmentName LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')
                OR fuct_GetDevicePosition(c.ResourceStructureId) LIKE CONCAT('%', #{equipmentMaskFilterDTO.keywords}, '%')))
            </if>
            <if test="equipmentMaskFilterDTO.resourceStructureIdList != null and equipmentMaskFilterDTO.resourceStructureIdList.size >0">
                AND c.ResourceStructureId IN
                <foreach collection="equipmentMaskFilterDTO.resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
                    #{resourceStructureId}
                </foreach>
            </if>
            <if test="equipmentMaskFilterDTO.equipmentBaseTypeIdList != null and equipmentMaskFilterDTO.equipmentBaseTypeIdList.size > 0">
                AND d.EquipmentBaseType IN
                <foreach collection="equipmentMaskFilterDTO.equipmentBaseTypeIdList" item="equipmentBaseType" open="(" close=")" separator=",">
                    #{equipmentBaseType}
                </foreach>
            </if>
            <if test="equipmentMaskFilterDTO.equipmentCategoryIdList != null and equipmentMaskFilterDTO.equipmentCategoryIdList.size > 0">
                AND a.EquipmentCategory IN
                <foreach collection="equipmentMaskFilterDTO.equipmentCategoryIdList" item="equipmentCategoryId" open="(" close=")" separator=",">
                    #{equipmentCategoryId}
                </foreach>
            </if>
            AND a.equipmentId IN
            <foreach collection="equipmentMaskFilterDTO.equipmentIdList" item="equipmentId" open="(" close=")" separator=",">
                #{equipmentId}
            </foreach>
        </where>
    </select>
</mapper>