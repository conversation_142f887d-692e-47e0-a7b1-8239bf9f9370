<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EquipmentMapper">
    <resultMap id="equipmentStateMap" type="com.siteweb.monitoring.model.EquipmentState">
        <id column="StationId" property="stationId"/>
        <id column="EquipmentId" property="equipmentId"/>
        <id column="ConnectState" property="onlineState"  typeHandler="org.apache.ibatis.type.EnumOrdinalTypeHandler"/>
        <id column="onProject" property="onProject" />
        <id column="masked" property="masked" />
    </resultMap>
    <resultMap id="equipmentBasicDtoMap" type="com.siteweb.monitoring.dto.EquipmentBasicDto">
        <result column="EquipmentId" property="equipmentId" />
        <result column="EquipmentName" property="equipmentName" />
        <result column="equipmentBaseType" property="equipmentBaseType" />
        <result column="equipmentCategory" property="equipmentCategory" />
        <result column="vendor" property="vendor" />
        <result column="unit" property="unit" />
        <result column="equipmentStyle" property="equipmentStyle" />
        <result column="equipmentModule" property="equipmentModule" />
        <result column="equipmentNo" property="equipmentNo" />
        <result column="assetState" property="assetState" />
        <result column="buyDate" property="buyDate" />
        <result column="usedDate" property="usedDate" />
        <result column="usedLimit" property="usedLimit" />
        <result column="price" property="price" />
        <result column="ratedCapacity" property="ratedCapacity" />
        <result column="projectName" property="projectName" />
        <result column="Description" property="description" />
        <result column="ExtValue" property="extValue"/>
        <result column="photo" property="photo"/>
    </resultMap>
    <update id="updateBatchEquipmentOrder">
        <foreach collection="list" item="item" separator=";">
            UPDATE tbl_equipment SET DisplayIndex = #{item.displayIndex}
            WHERE EquipmentId = #{item.equipmentId}
        </foreach>
    </update>
    <update id="batchUpdate">
        <foreach collection="batchUpdateEquipmentList" item="item" separator=";">
            UPDATE tbl_equipment
            <trim prefix="set" suffixOverrides=",">
                <if test="item.equipmentCategory != null">
                    equipmentCategory = #{item.equipmentCategory},
                </if>
                <if test="item.vendor != null">
                    vendor = #{item.vendor},
                </if>
                <if test="item.unit != null">
                    unit = #{item.unit},
                </if>
                <if test="item.equipmentStyle != null">
                    equipmentStyle = #{item.equipmentStyle},
                </if>
                <if test="item.equipmentModule != null">
                    equipmentModule = #{item.equipmentModule},
                </if>
                <if test="item.assetState != null">
                    assetState = #{item.assetState},
                </if>
                <if test="item.buyDate != null">
                    buyDate = #{item.buyDate},
                </if>
                <if test="item.usedDate != null">
                    usedDate = #{item.usedDate},
                </if>
                <if test="item.usedLimit != null">
                    usedLimit = #{item.usedLimit},
                </if>
                <if test="item.price != null">
                    price = #{item.price},
                </if>
                <if test="item.ratedCapacity != null">
                    ratedCapacity = #{item.ratedCapacity},
                </if>
                <if test="item.projectName != null">
                    projectName = #{item.projectName},
                </if>
                <if test="item.description != null">
                    description = #{item.description},
                </if>
                <if test="item.photo != null">
                    photo = #{item.photo},
                </if>
                <if test="item.extValue != null">
                    extValue = #{item.extValue}::json,
                </if>
            </trim>
            where equipmentId = #{item.equipmentId}
        </foreach>
    </update>
    <update id="updateByEquipmentBasicDto">
        UPDATE tbl_equipment SET
        EquipmentName = #{equipmentName},
        EquipmentNo = #{equipmentNo},
        EquipmentModule = #{equipmentModule},
        EquipmentStyle = #{equipmentStyle},
        AssetState = #{assetState},
        Price = #{price},
        UsedLimit = #{usedLimit},
        UsedDate = #{usedDate},
        BuyDate = #{buyDate},
        Vendor = #{vendor},
        Unit = #{unit},
        EquipmentCategory = #{equipmentCategory},
        Description = #{description},
        RatedCapacity = #{ratedCapacity},
        ProjectName = #{projectName},
        photo = #{photo},
        ExtValue = #{extValue}::json
        WHERE EquipmentId = #{equipmentId}
    </update>

    <select id="getEquipmentState" resultMap="equipmentStateMap">
        select a.StationId, a.EquipmentId, a.ConnectState,a.equipmentCategory,
        case when b.StartTime &lt; now() AND b.EndTime &gt; now() then 1 else 0 end AS onProject,
        case when c.StartTime &lt; now() AND c.EndTime  &gt; now() then 1 else 0 end AS masked
        from tbl_equipment a Left join tbl_equipmentmaintain b on a.stationId = b.stationId and a.equipmentId = b.EquipmentId
        left join tbl_equipmentmask c on a.StationId = c.StationId  and a.EquipmentId = c.EquipmentId
    </select>
    <select id="getEquipmentResource" resultType="com.siteweb.monitoring.dto.ResourceObjectEntity">
        SELECT e.EquipmentId as objectId,
        7 as objectTypeId,
        e.EquipmentName as resourceName,
        r.ResourceStructureId as parentResourceStructureId,
        r.ResourceStructureId as ResourceStructureId,
        r.StructureTypeId as parentResourceStructureTypeId
        FROM tbl_equipment e
        INNER JOIN resourcestructure r ON e.ResourceStructureId = r.ResourceStructureId
    </select>
    <select id="findEquipmentBasic" resultMap="equipmentBasicDtoMap">
        SELECT equipment.EquipmentId,
        equipment.EquipmentName,
        template.equipmentBaseType,
        equipment.equipmentCategory,
        equipment.vendor,
        equipment.unit,
        equipment.equipmentStyle,
        equipment.equipmentModule,
        equipment.equipmentNo,
        equipment.assetState,
        equipment.buyDate,
        equipment.usedDate,
        equipment.usedLimit,
        equipment.price,
        equipment.ratedCapacity,
        equipment.projectName,
        equipment.Description,
        equipment.ExtValue,
        equipment.photo,
        resource.ResourceStructureId,
        resource.LevelOfPath
        FROM tbl_equipment equipment
        INNER JOIN resourcestructure resource ON equipment.ResourceStructureId = resource.ResourceStructureId
        INNER JOIN tbl_equipmenttemplate template ON equipment.EquipmentTemplateId = template.EquipmentTemplateId
        WHERE equipment.EquipmentId = #{equipmentId}
    </select>
    <select id="getEquipmentStatisticsByResourceStructureAndEquipmentBaseType" resultType="java.lang.Integer">
        SELECT count(DISTINCT  b.EquipmentId)   FROM tbl_equipment  b
        INNER JOIN tbl_equipmenttemplate te on  b.EquipmentTemplateId = te.EquipmentTemplateId
        LEFT JOIN resourcestructure c ON b.ResourceStructureId = c.ResourceStructureId
        LEFT JOIN resourcestructure d ON c.LevelOfPath LIKE CONCAT(d.LevelOfPath,'%')
        WHERE 1=1
        AND d.ResourceStructureId = COALESCE(#{resourceStructureId}, d.ResourceStructureId)
        AND d.StructureTypeId = COALESCE(#{pageCategory}, d.StructureTypeId)
        AND te.EquipmentBaseType  IN
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </select>
    <select id="getEquipmentStatisticsByReq" resultType="java.lang.Integer">
        SELECT count(DISTINCT  b.EquipmentId)   FROM tbl_equipment  b
        LEFT JOIN resourcestructure c ON b.ResourceStructureId = c.ResourceStructureId
        LEFT JOIN resourcestructure d ON c.LevelOfPath LIKE CONCAT(d.LevelOfPath,'%')
        WHERE 1=1
        AND d.ResourceStructureId = COALESCE(#{resourceStructureId}, d.ResourceStructureId)
        AND d.StructureTypeId = COALESCE(#{pageCategory}, d.StructureTypeId)
    </select>
    <select id="getAllEquipment" resultType="com.siteweb.monitoring.entity.Equipment">
        SELECT a.StationId,  a.EquipmentId,  a.EquipmentName,  a.EquipmentNo,  a.EquipmentModule,  a.EquipmentStyle,
        a.AssetState,  a.Price,  a.UsedLimit,  a.UsedDate,  a.BuyDate,  a.Vendor,  a.Unit,  a.EquipmentCategory,
        a.EquipmentType, a.EquipmentClass,  a.EquipmentState,  a.EventExpression,  a.StartDelay,  a.EndDelay,
        a.Property,  a.Description,  a.EquipmentTemplateId,  a.HouseId, a.MonitorUnitId,  a.WorkStationId,
        a.SamplerUnitId,  a.DisplayIndex,  a.ConnectState,  a.UpdateTime,  a.ParentEquipmentId,  a.RatedCapacity,
        a.InstalledModule, a.ProjectName,  a.ContractNo,  a.InstallTime,  a.EquipmentSN,  a.SO,  a.resourcestructureId,
        b.EquipmentBaseType,te.BaseEquipmentName AS equipmentBaseTypeName, r.ResourceStructureName, a.photo
        FROM tbl_equipment a left join tbl_EquipmentTemplate b on a.EquipmentTemplateId = b.EquipmentTemplateId
        left join tbl_equipmentbasetype te on b.EquipmentBaseType  = te.BaseEquipmentId
        inner join resourcestructure r on  a.resourcestructureId  = r.ResourceStructureId ;
    </select>
    <select id="getEquipmentsInBatch" resultType="com.siteweb.monitoring.entity.Equipment">
        SELECT a.StationId,  a.EquipmentId,  a.EquipmentName,  a.EquipmentNo,  a.EquipmentModule,  a.EquipmentStyle,
        a.AssetState,  a.Price,  a.UsedLimit,  a.UsedDate,  a.BuyDate,  a.Vendor,  a.Unit,  a.EquipmentCategory,
        a.EquipmentType, a.EquipmentClass,  a.EquipmentState,  a.EventExpression,  a.StartDelay,  a.EndDelay,
        a.Property,  a.Description,  a.EquipmentTemplateId,  a.HouseId, a.MonitorUnitId,  a.WorkStationId,
        a.SamplerUnitId,  a.DisplayIndex,  a.ConnectState,  a.UpdateTime,  a.ParentEquipmentId,  a.RatedCapacity,
        a.InstalledModule, a.ProjectName,  a.ContractNo,  a.InstallTime,  a.EquipmentSN,  a.SO,  a.resourcestructureId,
        b.EquipmentBaseType,te.BaseEquipmentName AS equipmentBaseTypeName, r.ResourceStructureName, a.photo
        FROM tbl_equipment a left join tbl_EquipmentTemplate b on a.EquipmentTemplateId = b.EquipmentTemplateId
        left join tbl_equipmentbasetype te on b.EquipmentBaseType  = te.BaseEquipmentId
        inner join resourcestructure r   on  a.resourcestructureId  = r.ResourceStructureId
        where a.EquipmentId  IN
        <foreach collection="list" item="groupId" open="(" close=")" separator=",">
            #{groupId}
        </foreach>
    </select>
    <select id="getToDynamicApplyEquipmentVOs"
            resultType="com.siteweb.monitoring.vo.ToDynamicApplyEquipmentVO">
        SELECT A.ResourceStructureId, fuct_GetDevicePosition(A.ResourceStructureId) as EquipmentPosition, C.StationId, C.StationName,
        A.EquipmentId,A.EquipmentName,B.EquipmentTemplateId,B.EquipmentTemplateName FROM TBL_Equipment A
        INNER JOIN TBL_EquipmentTemplate B ON A.EquipmentTemplateId = B.EquipmentTemplateId
        INNER JOIN TBL_Station C ON A.StationId=C.StationId
        INNER JOIN  (SELECT ProtocolCode FROM TBL_EquipmentTemplate WHERE EquipmentTemplateId in (SELECT EquipmentTemplateId FROM TBL_Equipment WHERE StationId = #{stationId} AND EquipmentId = #{equipmentId})) P
        ON B.ProtocolCode = P.ProtocolCode
        WHERE A.EquipmentId != #{equipmentId}
    </select>

    <select id="findStationPageEquipmentDTOByStationId" resultType="com.siteweb.monitoring.dto.StationEquipmentDTO">
        select t1.StationId, t1.StationName, t2.EquipmentId, t2.EquipmentName,t2.ResourceStructureId, t2.equipmentStyle,t2.Vendor,
        t2.ratedCapacity,BaseEquipmentName as equipmentType, t5.ItemValue as StationCategory, t6.ItemValue as StationGrade, t2.EquipmentCategory as EquipmentCategoryId,
        t7.ItemValue AS EquipmentCategoryName,t2.HouseId
        from
        tbl_station t1 inner join tbl_equipment t2 on t1.StationId = t2.StationId
        inner join tbl_equipmentTemplate t3 on t2.EquipmentTemplateId = t3.EquipmentTemplateId
        left join tbl_equipmentbasetype t4 on t3.EquipmentBaseType = t4.BaseEquipmentId
        inner join tbl_dataitem t5 on t1.StationCategory = t5.ItemId and t5.EntryId = 71
        inner join tbl_dataitem t6 on t1.StationGrade = t6.ItemId and t6.EntryId = 2
        left join tbl_dataitem t7 on t2.EquipmentCategory = t7.ItemId and t7.EntryId = 7
        where t1.StationId =  #{stationId,jdbcType=INTEGER}
    </select>
    <select id="findByEquipmentTemplateIds" resultType="com.siteweb.monitoring.entity.Equipment">
        SELECT stationid, equipmentid, equipmentname, equipmentno, equipmentmodule, equipmentstyle, assetstate, price,
        usedlimit, useddate, buydate, vendor, unit, equipmentcategory, equipmenttype, equipmentclass, equipmentstate,
        eventexpression, startdelay, enddelay, property, description, equipmenttemplateid, houseid, monitorunitid,
        workstationid, samplerunitid, displayindex, connectstate, updatetime, parentequipmentid, ratedcapacity,
        installedmodule, projectname, contractno, installtime, equipmentsn, so, resourcestructureid FROM tbl_equipment
        WHERE EquipmentTemplateId IN
        <foreach collection="equipmentTemplateIdList" item="equipmentTemplateId" open="(" close=")" separator=",">
            #{equipmentTemplateId}
        </foreach>
    </select>
    <select id="findEquipmentsBySignalBaseTypeIds" resultType="com.siteweb.monitoring.dto.SimpleEquipmentDTO">
        SELECT DISTINCT b.EquipmentId, b.EquipmentName
        FROM tbl_Equipment b
        INNER JOIN tbl_Signal c ON b.EquipmentTemplateId = c.EquipmentTemplateId
        WHERE c.BaseTypeId IN
        <foreach collection="signalBaseTypeIdList" item="signalBaseTypeId" open="(" close=")" separator=",">
            #{signalBaseTypeId}
        </foreach>
        AND b.ResourceStructureId IN
        <foreach collection="resourceStructureIdList" item="resourceStructureId" open="(" close=")" separator=",">
            #{resourceStructureId}
        </foreach>
    </select>
    <select id="findByEquipmentId" resultType="com.siteweb.monitoring.entity.Equipment">
        SELECT a.StationId,
               a.EquipmentId,
               a.EquipmentName,
               a.EquipmentNo,
               a.EquipmentModule,
               a.EquipmentStyle,
               a.AssetState,
               a.Price,
               a.UsedLimit,
               a.UsedDate,
               a.BuyDate,
               a.Vendor,
               a.Unit,
               a.EquipmentCategory,
               a.EquipmentType,
               a.EquipmentClass,
               a.EquipmentState,
               a.EventExpression,
               a.StartDelay,
               a.EndDelay,
               a.Property,
               a.Description,
               a.EquipmentTemplateId,
               a.HouseId,
               a.MonitorUnitId,
               a.WorkStationId,
               a.SamplerUnitId,
               a.DisplayIndex,
               a.ConnectState,
               a.UpdateTime,
               a.ParentEquipmentId,
               a.RatedCapacity,
               a.InstalledModule,
               a.ProjectName,
               a.ContractNo,
               a.InstallTime,
               a.EquipmentSN,
               a.SO,
               a.resourcestructureId,
               b.EquipmentBaseType,
               te.BaseEquipmentName AS equipmentBaseTypeName,
               r.ResourceStructureName,
               a.photo
        FROM tbl_equipment a
                 LEFT JOIN tbl_EquipmentTemplate b ON a.EquipmentTemplateId = b.EquipmentTemplateId
                 LEFT JOIN tbl_equipmentbasetype te ON b.EquipmentBaseType = te.BaseEquipmentId
                 INNER JOIN resourcestructure r ON a.resourcestructureId = r.ResourceStructureId
        WHERE a.EquipmentId = #{equipmentId}
    </select>
    <select id="getAirConditionlist" resultType="com.siteweb.monitoring.entity.Equipment">
        select * from tbl_equipment where EquipmentName like '%空调%';
    </select>
    <update id="batchUpdateEquipmentUseDate">
        <foreach collection="selectedEquips" item="item" separator=";">
            UPDATE tbl_equipment SET UsedDate = #{useDate},UsedLimit = #{useLimit}
            WHERE EquipmentId = #{item}
        </foreach>
    </update>
    <select id="getEquipmentIdWithEquipmentStyleName"
            resultType="com.siteweb.monitoring.dto.EquipmentLegderDTO">
        SELECT
        te.stationId,
        te.EquipmentId,
        te.EquipmentStyle,
        te.EquipmentName,
        te.Vendor AS brand,
        te.EquipmentCategory,
        te.extValue,
        te.buyDate,
        te.equipmentSn,
        td.ItemValue AS equipmentCategoryName,
        r.ResourceStructureId,
        r.levelOfPath
        FROM
        tbl_equipment te
        INNER JOIN
        resourcestructure r ON r.ResourceStructureId = te.ResourceStructureId
        INNER JOIN
        tbl_dataitem td ON td.itemid = te.EquipmentCategory
        WHERE
        te.EquipmentStyle IS NOT NULL
        AND td.EntryId = 7

        <if test="styleName != null and styleName != ''">
            AND te.EquipmentStyle = #{styleName}
        </if>
    </select>
    <select id="getEquipmentStateWithSignalPoint"
            resultType="com.siteweb.monitoring.dto.EquipmentStateSignalPointDTO">
        SELECT
        te.stationId,
        te.EquipmentId,
        te.EquipmentStyle,
        te.EquipmentName,
        te.Vendor AS brand,
        te.EquipmentCategory,
        te.EquipmentTemplateid,
        r.ResourceStructureId,
        r.levelOfPath,
        signalcount.count As signalPointCount
        FROM
        (SELECT ts.EquipmentTemplateId, COUNT(*) AS count
        FROM tbl_signal ts
        GROUP BY ts.EquipmentTemplateId) AS signalcount
        INNER JOIN tbl_equipment te ON te.EquipmentTemplateId = signalcount.EquipmentTemplateId
        INNER JOIN resourcestructure r ON r.ResourceStructureId = te.ResourceStructureId
        <where>
            <if test="equipmentIds != null and equipmentIds.size() > 0">
                te.EquipmentId IN
                <foreach collection="equipmentIds" item="id" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>

    </select>


</mapper>