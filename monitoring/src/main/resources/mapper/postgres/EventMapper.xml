<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EventMapper">

    <resultMap id="eventConfigMap" type="com.siteweb.monitoring.dto.ConfigEventDTO">
        <id column="equipmentTemplateId" property="equipmentTemplateId"/>
        <id column="eventId" property="eventId"/>
        <result column="eventName" property="eventName"/>
        <result column="startType" property="startType"/>
        <result column="endType" property="endType"/>
        <result column="startExpression" property="startExpression"/>
        <result column="suppressExpression" property="suppressExpression"/>
        <result column="eventCategory" property="eventCategory"/>
        <result column="signalId" property="signalId"/>
        <result column="enable" property="enable"/>
        <result column="visible" property="visible"/>
        <result column="displayIndex" property="displayIndex"/>
        <!-- column 属性对应来自一方（一对多的一）表主键的字段名 -->
        <collection property="eventConditions" ofType="com.siteweb.monitoring.dto.EventConditionDTO"
                    column="equipmentTemplateId,eventId">
            <result column="equipmentTemplateId" property="equipmentTemplateId"/>
            <result column="eventId" property="eventId"/>
            <result column="EventConditionId" property="eventConditionId"/>
            <result column="StartOperation" property="startOperation"/>
            <result column="StartCompareValue" property="startCompareValue"/>
            <result column="Meanings" property="meanings"/>
            <result column="BaseTypeId" property="baseTypeId"/>
            <result column="BaseTypeName" property="baseTypeName"/>
        </collection>
    </resultMap>
    <insert id="batchInsert">
        INSERT INTO tbl_event(equipmenttemplateid, eventid, eventname, starttype, endtype, startexpression,
        suppressexpression, eventcategory, signalid, enable, visible, description, displayindex,
        moduleno)
        VALUES
        <foreach collection="eventList" item="item" separator=",">
            (#{item.equipmentTemplateId},#{item.eventId},#{item.eventName},#{item.startType},#{item.endType},#{item.startExpression},
            #{item.suppressExpression},#{item.eventCategory},#{item.signalId},#{item.enable},#{item.visible},#{item.description},#{item.displayIndex},
            #{item.moduleNo})
        </foreach>
    </insert>
    <update id="updateEventByConfig">
        UPDATE TBL_Event
        SET EventName     = #{event.eventName},
            StartType     = #{event.startType},
            EndType       = #{event.endType},
            EventCategory = #{event.eventCategory},
            DisplayIndex  = #{event.displayIndex}
        WHERE EquipmentTemplateId = #{event.equipmentTemplateId}
          AND EventId = #{event.eventId};
    </update>
    <select id="findEventsByStationIdAndEquipmentId" resultType="com.siteweb.monitoring.entity.Event">
        SELECT ts.id, ts.equipmenttemplateid, ts.eventid,ts.EventName AS eventname, ts.starttype, ts.endtype, ts.startexpression,
        ts.suppressexpression, ts.eventcategory, ts.signalid, ts.enable, ts.visible, ts.description, ts.displayindex,
        ts.moduleno
        FROM tbl_event ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        WHERE te.StationId=#{stationId} AND te.EquipmentId=#{equipmentId} AND ts.Visible = 1
    </select>
    <select id="findConfigEventByEquipmentTemplateId" resultMap="eventConfigMap">
        SELECT a.equipmenttemplateid,
               a.eventid,
               a.eventname,
               a.starttype,
               a.endtype,
               a.startexpression,
               a.suppressexpression,
               a.eventcategory,
               a.signalid,
               a.enable,
               a.visible,
               a.displayindex,
               b.EventConditionId,
               b.StartOperation,
               b.StartCompareValue,
               b.Meanings,
               b.BaseTypeId
        FROM tbl_event a
                 LEFT JOIN tbl_eventcondition b ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.EventId = b.EventId
                 LEFT JOIN tbl_eventbasedic c ON b.BaseTypeId = c.BaseTypeId
        WHERE a.EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findSimpleEventDTOs" resultType="com.siteweb.monitoring.dto.SimpleEventDTO">
        SELECT DISTINCT EventId, EventName FROM tbl_event
    </select>
    <select id="findSimpleEventDTOsByEquipmentId" resultType="com.siteweb.monitoring.dto.SimpleEventDTO">
        SELECT b.StationId, b.EquipmentId, a.EventId, a.EventName FROM tbl_event a inner join TBL_Equipment b
        ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE b.EquipmentId = #{equipmentId}
    </select>
    <select id="findConfigEventDTOByEventId" resultType="com.siteweb.monitoring.dto.ConfigEventDTO">
        SELECT * FROM TBL_Event a, TBL_Equipment b
        WHERE b.EquipmentTemplateId = a.EquipmentTemplateId
        AND a.EventId = #{eventId}
        AND b.EquipmentId = #{equipmentId}
    </select>

    <parameterMap id="saveEventTemplateParamMap" type="java.util.HashMap">
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EventId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="SourceTemplateId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="DestTemplateId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="StartType" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EndType" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EventName" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="StartExpression" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="SuppressExpression" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="EventCategory" mode="IN" jdbcType="DOUBLE"></parameter>
        <parameter property="DisplayIndex" mode="IN" jdbcType="DOUBLE"></parameter>
    </parameterMap>
    <select id="findConfigEventDTOBySignalId" resultType="com.siteweb.monitoring.dto.ConfigEventDTO">
        SELECT * FROM TBL_Event a, TBL_Equipment b
        WHERE b.EquipmentTemplateId = a.EquipmentTemplateId
        AND a.SignalId = #{signalId}
        AND b.EquipmentId = #{equipmentId}
    </select>
    <select id="findMaxEventIdByTemplateId" resultType="java.lang.Integer">
        SELECT max(EventId) FROM tbl_event WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>
    <select id="findEventsByEquipmentIdAndEventIds" resultType="com.siteweb.monitoring.dto.EquipmentEventDto">
        SELECT a.*,b.EquipmentId FROM tbl_event a
        INNER JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateid
        WHERE b.EquipmentId = #{equipmentId} AND eventId IN
        <foreach collection="eventIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findEventsByEquipmentIdAndSignalIds" resultType="com.siteweb.monitoring.dto.SimpleEventSignalDTO">
        SELECT a.signalId,a.EventId,a.EventName,b.EquipmentId FROM TBL_Event a, TBL_Equipment b
        WHERE b.EquipmentTemplateId = a.EquipmentTemplateId
        AND b.EquipmentId = #{eventRequestBySignalId.equipmentId}
        AND a.signalId IN
        <foreach collection="eventRequestBySignalId.signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="findSimpleEventDTOsByEquipmentIds" resultType="com.siteweb.monitoring.dto.SimpleEventDTO">
        SELECT DISTINCT EventId, EventName FROM tbl_event event
        INNER JOIN tbl_equipment equipment ON event.EquipmentTemplateId = equipment.EquipmentTemplateid
        WHERE equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="findEventsByEventRequestByCondition" resultType="com.siteweb.monitoring.dto.SimpleEventDTO">
        SELECT DISTINCT event.EventId, event.EventName FROM tbl_event event
        INNER JOIN tbl_equipment equipment ON event.EquipmentTemplateId = equipment.EquipmentTemplateid
        INNER JOIN tbl_eventcondition eventCondition ON eventCondition.EventId = event.EventId AND eventCondition.EquipmentTemplateId = event.EquipmentTemplateId
        INNER JOIN tbl_equipmenttemplate template ON template.EquipmentTemplateId = equipment.EquipmentTemplateid
        WHERE equipment.EquipmentId IN
        <foreach collection="eventRequestByCondition.equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        <if test="eventRequestByCondition.resourceStructureIds != null and eventRequestByCondition.resourceStructureIds.size > 0">
            AND equipment.ResourceStructureId IN
            <foreach collection="eventRequestByCondition.resourceStructureIds" item="resourceStructureId" open="(" close=")" separator=",">
                #{resourceStructureId}
            </foreach>
        </if>
        <if test="eventRequestByCondition.baseTypeIds != null and eventRequestByCondition.baseTypeIds.size > 0">
            AND eventCondition.baseTypeId IN
            <foreach collection="eventRequestByCondition.baseTypeIds" item="baseTypeId" open="(" close=")" separator=",">
                #{baseTypeId}
            </foreach>
        </if>
        <if test="eventRequestByCondition.baseEquipmentIds != null and eventRequestByCondition.baseEquipmentIds.size > 0">
            AND template.EquipmentBaseType IN
            <foreach collection="eventRequestByCondition.baseEquipmentIds" item="baseEquipmentId" open="(" close=")" separator=",">
                #{baseEquipmentId}
            </foreach>
        </if>
    </select>
    <select id="findAllConfigEvents" resultMap="eventConfigMap">
        SELECT a.equipmenttemplateid,
               a.eventid,
               a.eventname,
               a.starttype,
               a.endtype,
               a.startexpression,
               a.suppressexpression,
               a.eventcategory,
               a.signalid,
               a.enable,
               a.visible,
               a.displayindex,
               b.EventConditionId,
               b.StartOperation,
               b.StartCompareValue,
               b.Meanings,
               b.BaseTypeId,
               c.BaseTypeName
        FROM tbl_event a
                 LEFT JOIN tbl_eventcondition b
                           ON a.EquipmentTemplateId = b.EquipmentTemplateId AND a.EventId = b.EventId
                 LEFT JOIN tbl_eventbasedic c
                           ON b.BaseTypeId = c.BaseTypeId
    </select>
    <select id="findSelfDiagnoseEventConfigByWorkStationType"
            resultType="com.siteweb.monitoring.dto.SelfDiagnoseEventConfig">
        SELECT ws.WorkStationId,
               ws.ConnectState,
               ee.StationId,
               ee.EquipmentId,
               et.EventId,
               ec.EventConditionId,
               ec.BaseTypeId,
               ec.Meanings
        FROM TBL_WorkStation ws
                 INNER JOIN TBL_Event et ON ws.WorkStationId = et.EventId
                 INNER JOIN TBL_EventCondition ec
                            ON et.EventId = ec.EventId AND et.EquipmentTemplateId = ec.EquipmentTemplateId
                 INNER JOIN TBL_Equipment ee ON et.EquipmentTemplateId = ee.EquipmentTemplateId
        WHERE ws.isUsed = 1
          AND WorkStationType = #{workstationType}
          AND ee.EquipmentCategory = 99
    </select>
</mapper>