<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ControlMeaningsMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_controlmeanings(equipmenttemplateid, controlid, parametervalue, meanings, basecondid) VALUES
        <foreach collection="controlMeaningsList" item="controlMeanings" separator=",">
            (#{controlMeanings.equipmentTemplateId},#{controlMeanings.controlId},#{controlMeanings.parameterValue},#{controlMeanings.meanings},#{controlMeanings.baseCondId})
        </foreach>
    </insert>
    <select id="getControlMeaningsByEquipmentId" resultType="com.siteweb.monitoring.entity.ControlMeanings">
        SELECT ts.id, ts.equipmenttemplateid, ts.controlid, ts.parametervalue, ts.meanings, ts.basecondid
        FROM tbl_controlmeanings ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId=#{equipmentId}
    </select>
    <select id="findControlMeaningsDTOsByControlId"
            resultType="com.siteweb.monitoring.dto.ControlMeaningsDTO">
        SELECT te.StationId, te.EquipmentId, ts.ControlId, ts.ParameterValue, ts.Meanings
        FROM tbl_controlmeanings ts inner join TBL_Equipment te on ts.EquipmentTemplateId = te.EquipmentTemplateId
        where te.EquipmentId = #{equipmentId} and ts.ControlId = #{controlId}
    </select>
</mapper>