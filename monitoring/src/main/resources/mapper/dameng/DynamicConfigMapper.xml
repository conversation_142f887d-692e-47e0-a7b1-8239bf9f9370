<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.DynamicConfigMapper">
    <insert id="insertDynamicConfig">
        INSERT INTO TBL_DynamicConfig(UserId, ConfigTime, StationId, HostId, SyncXml)
        VALUES(#{userId}, now(), #{stationId}, #{hostId}, #{syncXml})
    </insert>
    <parameterMap id="generateMUSyncPlanParamMap" type="java.util.HashMap">
        <parameter property="StationId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="MonitorUnitId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="PlanTime" mode="IN" jdbcType="TIMESTAMP"></parameter>
    </parameterMap>
</mapper>