<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EquipmentBranchMapper">
    <insert id="batchInsert">
        INSERT INTO tbl_equipmentbranch(branchid, branchname, equipmentid, equipmentname) VALUES
        <foreach collection="equipmentBranchList" item="item" separator=",">
            (#{item.branchId},#{item.branchName},#{item.equipmentId},#{item.equipmentName})
        </foreach>
    </insert>
    <update id="batchUpdate">
        <foreach collection="equipmentBranchList" item="item">
            UPDATE tbl_equipmentbranch SET EquipmentId = #{item.equipmentId},branchId = #{item.branchId},
            EquipmentName =#{item.equipmentName},branchName = #{item.branchName}
            where id = #{item.id};
        </foreach>
    </update>
</mapper>