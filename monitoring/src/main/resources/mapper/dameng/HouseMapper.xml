<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.HouseMapper">
    <select id="findHouseInfo" resultType="com.siteweb.monitoring.dto.HouseInfoDTO">
        SELECT s."StationId", s."StationName", h."HouseId", h."HouseName", h."Description", h."LastUpdateDate"
        FROM "TBL_House" h
        INNER JOIN "TBL_Station" s ON h."StationId" = s."StationId"
        <where>
            <if test="stationIdList != null and stationIdList.size() > 0">
                h."StationId" IN
                <foreach collection="stationIdList" item="stationId" open="(" separator="," close=")">
                    #{stationId}
                </foreach>
            </if>
        </where>

    </select>


</mapper>