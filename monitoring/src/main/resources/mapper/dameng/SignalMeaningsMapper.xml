<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.SignalMeaningsMapper">
    <insert id="insertSignalMeanings">
        INSERT INTO TBL_SignalMeanings(EquipmentTemplateId, SignalId, StateValue, Meanings, BaseCondId)
        VALUES(#{equipmentTemplateId}, #{signalId}, #{stateValue}, #{meanings}, #{baseCondId})
    </insert>
    <insert id="batchInsert">
        INSERT INTO TBL_SignalMeanings(equipmenttemplateid, signalid, statevalue, meanings, basecondid) VALUES
        <foreach collection="signalMeanings" item="item" separator=",">
           (#{item.equipmentTemplateId},#{item.signalId},#{item.stateValue},#{item.meanings},#{item.baseCondId})
        </foreach>
    </insert>
    <update id="updateSignalMeanings">
        UPDATE TBL_SignalMeanings SET Meanings = #{meanings}, BaseCondId = #{baseCondId}
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND SignalId = #{signalId} AND StateValue = #{stateValue}
    </update>
    <delete id="deleteSignalMeaningsById">
        DELETE FROM TBL_SignalMeanings
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND SignalId = #{signalId} AND StateValue = #{stateValue}
    </delete>
    <select id="findSignalMeaningDTOBySignalId" resultType="com.siteweb.monitoring.dto.SignalMeaningDTO">
        SELECT a.* FROM TBL_SignalMeanings a INNER JOIN TBL_Equipment b
        ON a.EquipmentTemplateId = b.EquipmentTemplateId
        WHERE a.SignalId = #{signalId}
        AND b.EquipmentId = #{equipmentId}
    </select>
    <select id="findSignalsByEquipmentAndSignalIds" resultType="com.siteweb.monitoring.dto.EquipmentSignalMeaningsDTO">
        SELECT a.Id, a.EquipmentTemplateId, a.SignalId, a.StateValue, a.Meanings, a.BaseCondId,b.EquipmentId
        FROM tbl_signalmeanings a inner JOIN tbl_equipment b ON a.EquipmentTemplateId = b.EquipmentTemplateid
        WHERE b.EquipmentId = #{equipmentId}
        AND signalId IN
        <foreach collection="signalIds" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
</mapper>