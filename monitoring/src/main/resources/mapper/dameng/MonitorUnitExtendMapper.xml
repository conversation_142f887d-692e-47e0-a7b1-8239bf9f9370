<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.MonitorUnitExtendMapper">

    <select id="findExtendFiled1ByIp" resultType="java.lang.String">
        SELECT a.ExtendFiled1 FROM tsl_monitorunitextend a INNER JOIN tsl_monitorunit b ON a.MonitorUnitId = b.MonitorUnitId
        WHERE b.IpAddress = #{ip}
    </select>
</mapper>