<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ConfigChangeMacroLogMapper">
    <parameterMap id="saveConfigChangeLogParamMap" type="java.util.HashMap">
        <parameter property="ObjectId" mode="IN" jdbcType="VARCHAR"></parameter>
        <parameter property="ConfigId" mode="IN" jdbcType="INTEGER"></parameter>
        <parameter property="EditType" mode="IN" jdbcType="INTEGER"></parameter>
    </parameterMap>
    <select id="findEquipmentChangeMicroLogByUpdateTime" resultType="com.siteweb.monitoring.entity.ConfigChangeMicroLog">
        SELECT ObjectId, ConfigId, EditType, UpdateTime FROM tbl_configchangemicrolog
        WHERE ConfigId IN (0,1,3,28) AND UpdateTime &gt;  #{startTime} AND UpdateTime &lt; #{endTime}
    </select>
    <select id="findEquipmentTemplateChangeLogByUpdateTime" resultType="java.lang.Integer">
        select distinct(ObjectId) ObjectId from TBL_ConfigChangeMacroLog
        WHERE ConfigId IN (6,7,8,9) AND UpdateTime &gt;  #{startTime} AND UpdateTime &lt; #{endTime}
    </select>
    <select id="findResourceStructureChangeLogByUpdateTime" resultType="java.lang.Integer">
        select distinct(ObjectId) ObjectId from TBL_ConfigChangeMacroLog
        WHERE ConfigId=27 AND UpdateTime &gt;  #{startTime} AND UpdateTime &lt; #{endTime}
    </select>
    <select id="findStationChangeLogByUpdateTime" resultType="java.lang.Integer">
        select distinct(ObjectId) ObjectId from TBL_ConfigChangeMacroLog
        WHERE ConfigId=1 AND UpdateTime &gt;  #{startTime} AND UpdateTime &lt; #{endTime}
    </select>
    <select id="findEquipmentChangeMacroLogByUpdateTime" resultType="com.siteweb.monitoring.entity.ConfigChangeMacroLog">
        SELECT ObjectId, ConfigId, EditType, UpdateTime FROM TBL_ConfigChangeMacroLog
        WHERE ConfigId IN (0,1,3,28) AND UpdateTime &gt;  #{startTime} AND UpdateTime &lt; #{endTime}
    </select>
</mapper>