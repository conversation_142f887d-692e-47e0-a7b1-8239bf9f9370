<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.StationMaskMapper">
    <delete id="deleteStationMask">
        DELETE FROM TBL_StationMask WHERE StationId = #{stationId};
        DELETE FROM TBL_TimeGroupSpan WHERE TimeGroupId = #{stationId};
    </delete>
    <select id="getStationMaskDTOByStationId" resultType="com.siteweb.monitoring.dto.StationMaskDTO">
        SELECT a.*,b.UserName,c.stationName
        FROM tbl_StationMask a LEFT JOIN tbl_account b ON a.userId = b.userId
        LEFT JOIN tbL_station c ON a.StationId = c.StationId
        WHERE a.stationId = #{stationId}
    </select>
</mapper>