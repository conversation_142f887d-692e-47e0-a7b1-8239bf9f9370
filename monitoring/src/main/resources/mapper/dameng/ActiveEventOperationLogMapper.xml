<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ActiveEventOperationLogMapper">

    <resultMap id="baseResultMap" type="com.siteweb.monitoring.entity.ActiveEventOperationLog">
        <id property="activeEventOperationLogId" column="ActiveEventOperationLogId"/>
        <result property="sequenceId" column="SequenceId"/>
        <result property="stationId" column="StationId"/>
        <result property="equipmentId" column="EquipmentId"/>
        <result property="eventId" column="EventId"/>
        <result property="eventConditionId" column="EventConditionId"/>
        <result property="startTime" column="StartTime"/>
        <result property="operatorId" column="OperatorId"/>
        <result property="operation" column="Operation"/>
        <result property="operationTime" column="OperationTime"/>
        <result property="description" column="Description"/>
        <result property="userName" column="userName"/>
    </resultMap>
    <insert id="batchInsert">
        INSERT INTO activeeventoperationlog(sequenceid, stationid, equipmentid, eventid, eventconditionid, starttime,
        operatorid, operation, operationtime, description)
        VALUES
        <foreach collection="activeEventOperationLogList" item="log" separator=",">
            (#{log.sequenceId},#{log.stationId},#{log.equipmentId},#{log.eventId},#{log.eventConditionId},#{log.startTime},
             #{log.operatorId},#{log.operation},#{log.operationTime},#{log.description})
        </foreach>
    </insert>

    <select id="getCountBySequenceId" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM activeeventoperationlog
        WHERE SequenceId = #{sequenceId}
    </select>
    <select id="findBySequenceId" resultType="com.siteweb.monitoring.dto.ActiveEventOperationLogDTO">
        SELECT a.ActiveEventOperationLogId,
               a.SequenceId,
               a.StationId,
               a.EquipmentId,
               a.EventId,
               a.EventConditionId,
               a.StartTime,
               a.OperatorId,
               a.Operation,
               a.OperationTime,
               a.Description,
               b.UserName as Operator
        FROM ActiveEventOperationLog a
                 LEFT JOIN TBL_Account b ON a.OperatorId = b.UserId
        WHERE a.SequenceId = #{sequenceId}
    </select>

    <select id="findByWrapper" resultMap="baseResultMap">
        select t1.*, ta.UserName from activeeventoperationlog t1
        left join tbl_account ta on t1.OperatorId = ta.UserId
        <where>
            <if test="wrapper.operation != null and wrapper.operation != ''">
                AND t1.Operation like concat('%', #{wrapper.operation}, '%')
            </if>
            <if test="wrapper.operatorIds != null and wrapper.operatorIds.size > 0">
                AND t1.operatorId in
                <foreach collection="wrapper.operatorIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.equipmentIds != null and wrapper.equipmentIds.size > 0">
                AND t1.equipmentId in
                <foreach collection="wrapper.equipmentIds" item="item" open="(" close=")" separator=",">
                    #{item}
                </foreach>
            </if>
            <if test="wrapper.startDate != null">
                AND t1.OperationTime &gt;= #{wrapper.startDate}
            </if>
            <if test="wrapper.endDate != null">
                AND t1.OperationTime &lt;= #{wrapper.endDate}
            </if>
            <if test="wrapper.sql != null and wrapper.sql.size > 0">
                <foreach collection="wrapper.sql" item="sql">
                    ${sql}
                </foreach>
            </if>
        </where>
        order by t1.OperationTime desc
    </select>

    <select id="selectList" resultMap="baseResultMap">
        select t1.*, ta.UserName from activeeventoperationlog t1
        left join tbl_account ta on ta.UserId = t1.OperatorId
        <where>
            ${ew.sqlSegment}
        </where>
    </select>
</mapper>