<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ConfigChangeMapMapper">
    <select id="findByConfigIdAndEditType" resultType="com.siteweb.monitoring.entity.ConfigChangeMap">
        SELECT config.MacroConfigId, config.MacroEditType, config.IdConvertRule
        FROM TBL_ConfigChangeMap config
        WHERE config.MicroConfigId = #{configId}
          AND config.MicroEditType = #{editType}
        LIMIT 0,1;
    </select>
</mapper>