<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EquipmentTemplateMapper">
    <resultMap id="templateNameMap" type="com.siteweb.monitoring.entity.EquipmentTemplate">
        <result property="equipmentTemplateId" column="EquipmentTemplateId"/>
        <result property="equipmentTemplateName" column="EquipmentTemplateName"/>
    </resultMap>
    <select id="findNameByIds" resultMap="templateNameMap">
        SELECT EquipmentTemplateId,EquipmentTemplateName FROM tbl_equipmenttemplate WHERE EquipmentTemplateId IN
        <foreach collection="templateIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
    <select id="getSignalPoint" resultType="com.siteweb.monitoring.dto.EquipmentSignalPointStatisticsDTO">
        select tet.EquipmentCategory as equipmentCategory, td.ItemValue as equipmentCategoryName,tet.EquipmentTemplateId as equipmentTemplateId, tet.EquipmentTemplateName as equipmentTemplateName,signalcount.count as signalPointCount ,tet.equipmentStyle
        from tbl_equipmenttemplate tet
        LEFT JOIN tbl_dataitem td
        ON tet.EquipmentCategory = td.ItemId AND td.EntryId = 7
        inner join
        (select EquipmentTemplateId,count(*) as count from tbl_signal ts group by ts.EquipmentTemplateId) as signalcount
        on signalcount.EquipmentTemplateId = tet.EquipmentTemplateId
    </select>
    <select id="getSignalPointDetail" resultType="com.siteweb.monitoring.dto.TemplateSignalPointDTO">
        select tet.EquipmentTemplateId as equipmentTemplateId, td.ItemValue as equipmentCategoryName,tet.EquipmentTemplateName as equipmentTemplateName,ts.SignalId ,ts.ChannelNo ,ts.SignalName
        from tbl_equipmenttemplate tet
        inner join tbl_dataitem td on tet.EquipmentCategory = td.ItemId
        inner join tbl_signal ts on ts.EquipmentTemplateId  = tet.EquipmentTemplateId
        where td.EntryId = 7 and tet.EquipmentTemplateId in
        <foreach collection="list" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
        order by ts.DisplayIndex
    </select>
</mapper>