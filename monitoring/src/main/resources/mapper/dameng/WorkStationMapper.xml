<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.WorkStationMapper">
    <update id="updateWorkStationConnectStateById">
        UPDATE TBL_WorkStation SET ConnectState = #{connectState}, UpdateTime = CURRENT_TIMESTAMP WHERE WorkStationId = #{workStationId}
    </update>
    <select id="findWorkStationIdByType" resultType="java.lang.Integer">
        SELECT WorkStationId FROM TBL_WorkStation WHERE WorkStationType = #{workStationType}
    </select>
    <select id="networkTopology" resultType="com.siteweb.monitoring.dto.WorkStationNetworkTopologyDTO">
        SELECT station.WorkStationId,
               station.WorkStationName,
               station.WorkStationType,
               station.IPAddress,
               station.ParentId,
               station.ConnectState,
               station.UpdateTime,
               station.IsUsed,
               station.CPU,
               station.Memory,
               station.ThreadCount,
               station.DiskFreeSpace,
               station.DBFreeSpace,
               station.LastCommTime,
               structure.StructureName AS CenterName,
               structure.StructureId   AS CenterId
        FROM TBL_WorkStation station,
             TBL_StationStructure structure
        WHERE structure.ParentStructureId = 0
          AND station.IsUsed = 1;
    </select>
</mapper>