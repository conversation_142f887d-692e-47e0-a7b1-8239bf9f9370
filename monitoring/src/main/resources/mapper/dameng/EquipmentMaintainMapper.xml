<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.EquipmentMaintainMapper">

    <select id="getProjectEquipmentList" resultType="com.siteweb.monitoring.dto.EquipmentProjectDetail">
        SELECT d.StationId, d.StationName, b.EquipmentId,b.equipmentName, a.StartTime, a.EndTime, a.Description as Reason,a.UserId, c.HouseName
        FROM tbl_equipmentmaintain a inner join tbl_Equipment b on a.StationId = b.StationId and a.equipmentId = b.EquipmentId
        inner join TBL_House c on b.StationId = c.StationId and b.HouseId = c.HouseId
        INNER JOIN TBL_Station d on c.StationId = d.StationId
        where a.EquipmentState =#{equipmentState}
    </select>
    <select id="findMaintainStateByResourceStructure" resultType="com.siteweb.monitoring.dto.EquipmentMaintainDTO">
        SELECT a.StationId,
               a.EquipmentId,
               a.EquipmentName,
               b.EquipmentState,
               b.StartTime,
               b.EndTime,
               b.Description as reason
        FROM tbl_equipment a
                 LEFT JOIN tbl_equipmentmaintain b ON a.EquipmentId = b.EquipmentId
        WHERE a.ResourceStructureId = #{resourceStructureId}
    </select>
</mapper>