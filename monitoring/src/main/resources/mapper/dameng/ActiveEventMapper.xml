<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ActiveEventMapper">
    <sql id="Base_Column_List">
        SequenceId, StationId, StationName, EquipmentId, EquipmentName, EventId, EventName, EventConditionId,
        EventSeverityId,EventSeverity, EventLevel, StartTime, EndTime, CancelTime, CancelUserId, CancelUserName, ConfirmTime,
        ConfirmerId, ConfirmerName, EventValue, ReversalNum, Meanings, EventFilePath, Description, SourceHostId,
        InstructionId, InstructionStatus, StandardAlarmNameId, StandardAlarmName, BaseTypeId, BaseTypeName,
        EquipmentCategory, EquipmentCategoryName, MaintainState, SignalId, RelateSequenceId, EventCategoryId,
        EventStateId, CenterId, CenterName, StructureName, MonitorUnitName, StructureId, StationCategoryId,
        EquipmentVendor, EndValue, ConvergenceEventId, ResourceStructureId, BaseEquipmentId
    </sql>
    <update id="updateCancelInfo">
        UPDATE TBL_ActiveEvent
        SET CancelTime     = #{endTime},
            CancelUserId   = #{userId},
            CancelUserName = #{userName},
            Description    = #{note}
        WHERE SequenceId = #{sequenceId};
    </update>

    <sql id="AllActiveEventSql">
        SELECT
        a.SequenceId, a.StationId, a.StationName, a.EquipmentId, a.EquipmentName, a.EventId, a.EventName, a.EventConditionId,
        a.EventSeverity, a.EventLevel, a.StartTime, a.EndTime, a.CancelTime, a.CancelUserId, a.CancelUserName, a.ConfirmTime,
        a.ConfirmerId, a.ConfirmerName, a.EventValue, a.ReversalNum, a.Meanings, a.EventFilePath, a.Description, a.SourceHostId,
        a.InstructionId, a.InstructionStatus, a.StandardAlarmNameId, a.StandardAlarmName, a.BaseTypeId, a.BaseTypeName,
        a.EquipmentCategory, a.EquipmentCategoryName, a.MaintainState, a.SignalId, a.RelateSequenceId, a.EventCategoryId,
        a.EventStateId, a.CenterId, a.CenterName, a.StructureName, a.MonitorUnitName, a.StructureId, a.StationCategoryId,
        a.EquipmentVendor,
        a.EndValue, a.ConvergenceEventId, a.ResourceStructureId, a.BaseEquipmentId, b.BaseEquipmentName,a.EventSeverityId,
        a.EventReasonType
        FROM TBL_ActiveEvent a left join TBL_EquipmentBaseType b on a.BaseEquipmentId = b.BaseEquipmentId
    </sql>
    <select id="getAllActiveEvent" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        <include refid="AllActiveEventSql"/>
    </select>

    <select id="getAllActiveEventByEventLevel" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        <include refid="AllActiveEventSql"/>
        WHERE
        a.EventLevel in (
            SELECT
            CAST(td.ExtendField4 AS int)
            FROM
            tbl_dataitem td
            WHERE
            td.entryId = 23
            AND td.enable = 1
        )
    </select>

    <parameterMap id="confirmActiveEventParamMap" type="java.util.HashMap">
        <parameter property="Events" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="ConfirmerId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="Note" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <parameterMap id="confirmBdActiveEventParamMap" type="java.util.HashMap">
        <parameter property="Events" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="ConfirmerId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="Note" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="EventReasonType" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>

    <parameterMap id="opActiveEventParamMap" type="java.util.HashMap">
        <parameter property="Events" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="UserId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="Note" mode="IN" jdbcType="VARCHAR"/>
    </parameterMap>
    <parameterMap id="saveEventResponseParamMap" type="java.util.HashMap">
        <parameter property="StationId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="EquipmentId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="EventId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="EventConditionId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="SequenceId" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="StartTime" mode="IN" jdbcType="DATE"/>
        <parameter property="EndTime" mode="IN" jdbcType="DATE"/>
        <parameter property="Overturn" mode="IN" jdbcType="INTEGER"/>
        <parameter property="Meanings" mode="IN" jdbcType="VARCHAR"/>
        <parameter property="EventValue" mode="IN" jdbcType="FLOAT"/>
        <parameter property="BaseTypeId" mode="IN" jdbcType="INTEGER"/>
        <parameter property="ret" mode="OUT" jdbcType="INTEGER"/>
    </parameterMap>
    <select id="cancelActiveEvent" parameterMap="opActiveEventParamMap" statementType="CALLABLE">
        call S6_CancelEvent(?, ?, ?)
    </select>
    <select id="cancelBdActiveEvent" parameterMap="opActiveEventParamMap" statementType="CALLABLE">
        call BD_S6_CancelEvent(?, ?, ?)
    </select>
    <select id="findByConvergenceEventId" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        SELECT
        a.SequenceId, a.StationId, a.StationName, a.EquipmentId, a.EquipmentName, a.EventId, a.EventName, a.EventConditionId,
        a.EventLevel, a.EventSeverity, a.StartTime, a.EndTime, a.CancelTime, a.CancelUserId, a.CancelUserName, a.ConfirmTime,
        a.ConfirmerId, a.ConfirmerName, a.EventValue, a.ReversalNum, a.Meanings, a.EventFilePath, a.Description, a.SourceHostId,
        a.InstructionId, a.InstructionStatus, a.StandardAlarmNameId, a.StandardAlarmName, a.BaseTypeId, a.BaseTypeName,
        a.EquipmentCategory, a.EquipmentCategoryName, a.MaintainState, a.SignalId, a.RelateSequenceId, a.EventCategoryId,
        a.EventStateId, a.CenterId, a.CenterName, a.StructureName, a.MonitorUnitName, a.StructureId, a.StationCategoryId,
        a.EquipmentVendor,
        a.EndValue
        from TBL_ActiveEvent a
        WHERE ConvergenceEventId  =  #{convergenceEventId}
        ORDER BY StartTime DESC
    </select>
    <insert id="confirmActiveEvent" parameterMap="confirmActiveEventParamMap" statementType="CALLABLE">
        call S6_ConfirmedEvent(?, ?, ?)
    </insert>
    <insert id="confirmBdActiveEvent" parameterMap="confirmBdActiveEventParamMap" statementType="CALLABLE">
        call Bd_S6_ConfirmedEvent(?, ?, ?, ?)
    </insert>
    <update id="updateActiveEventByActiveEventInstructionDTO">
        UPDATE tbl_activeevent set InstructionId = #{instructionId}, InstructionStatus = #{instructionStatus} WHERE SequenceId = #{sequenceId}
    </update>
    <update id="batchUpdateConfirmEvent">
        <foreach collection="list" item="item" separator=";">
            UPDATE tbl_activeevent
            SET
            EndTime = #{item.endTime},
            ConfirmTime = #{item.confirmTime},
            ConfirmerId = #{item.confirmerId},
            ConfirmerName = #{item.confirmerName},
            Description = #{item.description},
            EventReasonType = #{item.eventReasonType}
            WHERE SequenceId = #{item.sequenceId}
        </foreach>
    </update>
    <select id="findUnConfirmBySequenceIdsForUpdate" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tbl_activeevent
        WHERE ConfirmTime IS NULL AND SequenceId IN
        <foreach collection="sequenceIds" item="sequenceId" open="(" close=")" separator=",">
            #{sequenceId}
        </foreach>
        FOR UPDATE
    </select>
    <select id="findBySequenceIdForUpdate" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tbl_activeevent
        WHERE SequenceId = #{sequenceId}
        FOR UPDATE
    </select>
    <select id="findUnEndBySequenceIdsForUpdate" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tbl_activeevent
        WHERE EndTime IS NULL AND SequenceId IN
        <foreach collection="sequenceIds" item="sequenceId" open="(" close=")" separator=",">
            #{sequenceId}
        </foreach>
        FOR UPDATE
    </select>
    <select id="findBySequenceIdsForUpdate" resultType="com.siteweb.monitoring.entity.ActiveEvent">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        tbl_activeevent
        WHERE SequenceId IN
        <foreach collection="sequenceIds" item="sequenceId" open="(" close=")" separator=",">
            #{sequenceId}
        </foreach>
        FOR UPDATE
    </select>
    <select id="findSequenceIdsByIdentities" resultType="java.lang.String">
        SELECT SequenceId FROM tbl_activeevent WHERE
        (StationId, EquipmentId, EventId) IN
        <foreach collection="eventIdentityDTOList" item="item" separator="," open="(" close=")">
            (#{item.stationId}, #{item.equipmentId}, #{item.eventId})
        </foreach>
    </select>
</mapper>
