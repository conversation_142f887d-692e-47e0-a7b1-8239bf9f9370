<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.ActiveControlMapper">
    <sql id="Base_Column_List">
        StationId, StationName, EquipmentId, EquipmentName, ControlId, ControlName, SerialNo, ControlSeverity, CmdToken,
        ControlPhase, StartTime, EndTime, ConfirmTime, ConfirmerId, ConfirmerName, ControlResultType, ControlResult, ControlExecuterId,
        ControlExecuterIdName, ControlType, ActionId, Description, Retry, BaseTypeId, BaseTypeName, ParameterValues, BaseCondId
    </sql>
    <select id="getActiveControlByEquipmentId" resultType="com.siteweb.monitoring.entity.ActiveControl">
        SELECT
        <include refid="Base_Column_List"/>
        FROM TBL_ActiveControl
        WHERE EquipmentId = #{equipmentId}
        ORDER BY StartTime DESC
    </select>
    <select id="findAllControl" resultType="com.siteweb.monitoring.dto.ControlDTO">
        SELECT fuct_GetDevicePosition(equipment.ResourceStructureId) AS equipmentPosition,
               equipment.EquipmentName,
               baseType.BaseEquipmentName,
               control.ControlName,
               control.StartTime   AS execTime,
               control.ConfirmTime AS responseTime,
               control.ControlExecuterIdName,
               control.controlResult      AS execResult
        FROM tbl_activecontrol control
                 INNER JOIN tbl_equipment equipment ON control.EquipmentId = equipment.EquipmentId
                 INNER JOIN tbl_equipmenttemplate template ON equipment.EquipmentTemplateid = template.EquipmentTemplateId
                 INNER JOIN tbl_equipmentbasetype baseType ON baseType.BaseEquipmentId = template.EquipmentBaseType
        WHERE control.StartTime &gt;= #{startTime} AND control.StartTime &lt;= #{endTime} AND equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
        UNION ALL
        SELECT fuct_GetDevicePosition(equipment.ResourceStructureId) AS equipmentPosition,
               equipment.EquipmentName,
               baseType.BaseEquipmentName,
               control.ControlName,
               control.StartTime   AS execTime,
               control.ConfirmTime AS responseTime,
               control.ControlExecuterIdName,
               control.controlResult      AS execResult
        FROM tbl_historycontrol control
                 INNER JOIN tbl_equipment equipment ON control.EquipmentId = equipment.EquipmentId
                 INNER JOIN tbl_equipmenttemplate template ON equipment.EquipmentTemplateid = template.EquipmentTemplateId
                 INNER JOIN tbl_equipmentbasetype baseType ON baseType.BaseEquipmentId = template.EquipmentBaseType
        WHERE control.StartTime &gt;= #{startTime} AND control.StartTime &lt;= #{endTime} AND equipment.EquipmentId IN
        <foreach collection="equipmentIds" item="equipmentId" open="(" close=")" separator=",">
            #{equipmentId}
        </foreach>
    </select>
    <select id="getDoorAccessPassword" resultType="java.lang.String">
        select `password` as password from tbl_door where stationId=#{stationId} and equipmentId=#{equipmentId}
    </select>
    <select id="findDoorControlType" resultType="java.lang.Integer">
        SELECT A.DoorControlId
        FROM TBL_Door A
                 INNER JOIN TBL_Equipment B ON A.StationId = B.StationId AND A.EquipmentId = B.EquipmentId
        WHERE B.EquipmentId = #{equipmentId};
    </select>
    <select id="existsActiveControl" resultType="java.lang.Boolean">
        SELECT COUNT(*) AS total
        FROM tbl_activecontrol
        WHERE controlExecuterId = #{userId}
          AND equipmentId = #{equipmentId}
          AND controlId = #{controlId}
          AND parameterValues = #{parameterValues}
          AND endTime IS NULL
    </select>
</mapper>