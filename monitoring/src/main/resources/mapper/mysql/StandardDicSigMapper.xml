<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.StandardDicSigMapper">
    <select id="findTelecomStandardSig" resultType="com.siteweb.monitoring.dto.IdValueDTO">
        SELECT CONVERT(FLOOR((StandardDicId % 100000000) / 1000), decimal(10, 0)) value,
               CONCAT(EquipmentLogicClass, '_', SignalStandardName)               label
        FROM TBL_StandardDicSig
        WHERE StandardDicId % 1000 = 1
          AND (FLOOR(StandardDicId / 100000000) = 1
            OR FLOOR(StandardDicId / 100000000) = 9)
          AND StandardType = 2
    </select>

    <select id="getTelecomSignalIdBySignalBaseEntryIds" resultType="com.siteweb.monitoring.entity.Signal">
        select distinct es.*
        from
        tbl_signal es
        inner join tbl_signalbasemap sbm on sbm.BaseTypeId = es.BaseTypeId
        inner join TBL_StandardDicSig sds on sds.StandardDicId = sbm.StandardDicId
        WHERE sds.StandardDicId % 1000 = 1
        AND sds.StandardType = 2
        AND (FLOOR(sds.StandardDicId / 100000000) = 1
        OR FLOOR(sds.StandardDicId / 100000000) = 9)
        <if test="entryIds != null and entryIds.size() > 0">
            and floor((sbm.StandardDicId %100000000)/1000) in
            <foreach collection="entryIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findErmStandardSig" resultType="com.siteweb.monitoring.dto.IdValueDTO">
        SELECT CONVERT(FLOOR(A.BaseTypeId / 1000), decimal(10.0)) value,
               CONCAT(B.BaseEquipmentName, '_', A.BaseTypeName)   label
        FROM TBL_SignalBaseDic A
                 INNER JOIN TBL_EquipmentBaseType B ON A.BaseEquipmentId = B.BaseEquipmentId
        WHERE A.BaseTypeId - FLOOR(A.BaseTypeId / 1000) * 1000 = 1
    </select>

    <select id="getErmSignalIdBySignalBaseEntryIds" resultType="com.siteweb.monitoring.entity.Signal">
        select * from tbl_signal
        <if test="entryIds != null and entryIds.size() > 0">
            where FLOOR(BaseTypeId / 1000) in
            <foreach collection="entryIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>

    <select id="findMobileStandardSig" resultType="com.siteweb.monitoring.dto.IdValueDTO">
        SELECT CONVERT(FLOOR((StandardDicId) / 1000), decimal(10, 0)) value,
               CONCAT(EquipmentLogicClass, '_', SignalStandardName)   label
        FROM TBL_StandardDicSig
        WHERE StandardDicId % 1000 = 1
          AND StandardType = 1
    </select>

    <select id="getMobileSignalIdBySignalBaseEntryIds" resultType="com.siteweb.monitoring.entity.Signal">
        select distinct es.*
        from
        tbl_signal es
        inner join tbl_signalbasemap sbm on sbm.BaseTypeId = es.BaseTypeId
        inner join TBL_StandardDicSig sds on sds.StandardDicId = sbm.StandardDicId
        WHERE sds.StandardDicId % 1000 = 1
        AND sds.StandardType = 1
        <if test="entryIds != null and entryIds.size() > 0">
            and FLOOR((sds.StandardDicId) / 1000) in
            <foreach collection="entryIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
    <select id="findUniComStandardSig" resultType="com.siteweb.monitoring.dto.IdValueDTO">
        SELECT CONVERT(FLOOR((StandardDicId) / 1000), decimal(10, 0)) value,
               CONCAT(EquipmentLogicClass, '_', SignalStandardName)   label
        FROM TBL_StandardDicSig
        WHERE StandardDicId % 100 = 1
          AND StandardType = 3
    </select>
    <select id="getUniComSignalIdBySignalBaseEntryIds" resultType="com.siteweb.monitoring.entity.Signal">
        select distinct es.*
        from
        tbl_signal es
        inner join tbl_signalbasemap sbm on sbm.BaseTypeId = es.BaseTypeId
        inner join TBL_StandardDicSig sds on sds.StandardDicId = sbm.StandardDicId
        WHERE sds.StandardDicId % 1000 = 1
        AND sds.StandardType = 3
        <if test="entryIds != null and entryIds.size() > 0">
            and FLOOR((sds.StandardDicId) / 1000) in
            <foreach collection="entryIds" item="item" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </if>
    </select>
</mapper>