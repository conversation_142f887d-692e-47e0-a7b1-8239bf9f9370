<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.StandardDicEventMapper">
    <select id="getAllStandardDicEvent" resultType="com.siteweb.monitoring.entity.StandardDicEvent">
        SELECT StandardDicId, StandardType, EquipmentLogicClassId, EquipmentLogicClass,
        EventLogicClassId, EventLogicClass, EventClass, EventStandardName, NetManageId,
        EventSeverity, CompareValue, StartDelay, Meanings, EquipmentAffect, BusinessAffect,
        StationCategory, ModifyType, Description, ExtendFiled1, ExtendFiled2, ExtendFiled3
        FROM tbl_standarddicevent
    </select>
    <select id="findStandardAlarmName" resultType="com.siteweb.monitoring.dto.StandardAlarmNameDTO">
        SELECT A.StandardDicId as standardAlarmNameId,
               replace(D.EventStandardName,'XX',convert( #{baseTypeId} - floor(#{baseTypeId} DIV 1000)*1000, CHAR(10))) as standardAlarmName
        FROM TBL_EventBaseMap A
                 INNER JOIN TBL_StationBaseMap B ON A.StationBaseType = B.StationBaseType AND B.StandardType = #{standardVer}
                 INNER JOIN TBL_Station C ON C.StationCategory = B.StationCategory
                 INNER JOIN TBL_StandardDicEvent D ON D.StandardDicId = A.StandardDicId
            AND (D.StationCategory = B.StationBaseType OR D.StationCategory = 0)
            AND D.StandardType = #{standardVer}
        WHERE A.BaseTypeId = floor(#{baseTypeId} DIV 1000) * 1000 + 1
          AND C.StationId = #{stationId} AND A.StandardType = #{standardVer};
    </select>
    <select id="findBaseAlarmByStandardVerAndBaseEquipmentid"
            resultType="com.siteweb.monitoring.dto.BaseAlarmDTO">
            SELECT
            teb.baseTypeId AS baseTypeId,
            teb.baseTypeName AS baseTypeName,
            tsd.equipmentLogicClassId AS equipmentLogicClassId,
            tsd.equipmentLogicClass AS equipmentLogicClass,
            tsd.eventStandardName AS eventStandardName,
            tsd.netManageId AS netManageId,
            tsd.stationCategory AS stationCategory,
            teb.baseNameExt AS baseNameExt,
            teb.baseEquipmentId As baseEquipmentId
            FROM
            tbl_EventBaseDic teb
            LEFT JOIN
            tbl_EventBaseMap te ON teb.BaseTypeId = te.BaseTypeId
            LEFT JOIN
            tbl_StandardDicEvent tsd ON tsd.StandardDicId = te.StandardDicId
            WHERE
            te.StandardType = #{standardVer}
            AND teb.baseEquipmentId = #{baseEquipmentId};
    </select>
    <select id="findBaseAlarmByStandardVerAndBaseAlarmId"
            resultType="com.siteweb.monitoring.dto.BaseAlarmDTO">
        SELECT
        teb.baseTypeId AS baseTypeId,
        teb.baseTypeName AS baseTypeName,
        tsd.equipmentLogicClassId AS equipmentLogicClassId,
        tsd.equipmentLogicClass AS equipmentLogicClass,
        tsd.eventStandardName AS eventStandardName,
        tsd.netManageId AS netManageId,
        tsd.stationCategory AS stationCategory,
        teb.baseNameExt AS baseNameExt,
        teb.baseEquipmentId As baseEquipmentId
        FROM
        tbl_EventBaseDic teb
        LEFT JOIN
        tbl_EventBaseMap te ON teb.BaseTypeId = te.BaseTypeId
        LEFT JOIN
        tbl_StandardDicEvent tsd ON tsd.StandardDicId = te.StandardDicId
        WHERE
        te.StandardType = #{standardVer}
        AND teb.BaseTypeId = #{baseTypeId};
    </select>
</mapper>