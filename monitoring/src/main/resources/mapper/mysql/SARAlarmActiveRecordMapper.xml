<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.SARAlarmActiveRecordMapper">
    <update id="updateBySequenceId">
        UPDATE TBL_SARAlarmActiveRecord SET
        <trim prefix="set" suffixOverrides=",">
            <if test="sarAlarmActiveRecord.endTime != null">
                EndTime = #{sarAlarmActiveRecord.endTime},
            </if>
            <if test="sarAlarmActiveRecord.meanings != null and sarAlarmActiveRecord.meanings != ''">
                Meanings = #{sarAlarmActiveRecord.meanings},
            </if>
            <if test="sarAlarmActiveRecord.overturn != null">
                Overturn = #{sarAlarmActiveRecord.overturn},
            </if>
            <if test="sarAlarmActiveRecord.eventValue != null">
                EventValue = #{sarAlarmActiveRecord.eventValue},
            </if>
        </trim>
        where sequenceId = #{sarAlarmActiveRecord.sequenceId}
    </update>
</mapper>