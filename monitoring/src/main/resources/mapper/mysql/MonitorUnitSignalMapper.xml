<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.MonitorUnitSignalMapper">
    <insert id="batchInsert">
        INSERT INTO tsl_monitorunitsignal(stationid, monitorunitid, equipmentid, signalid, referencesamplerunitid,
        referencechannelno, expression, instancetype) VALUES
        <foreach collection="monitorUnitSignals" item="item" separator=",">
            (#{item.stationId},#{item.monitorUnitId},#{item.equipmentId},#{item.signalId},#{item.referenceSamplerUnitId},
            #{item.referenceChannelNo},#{item.expression},#{item.instanceType})
        </foreach>
    </insert>
    <select id="findMonitorUnitSignal" resultType="com.siteweb.monitoring.entity.MonitorUnitSignal">
        SELECT stationid,
               monitorunitid,
               equipmentid,
               signalid,
               referencesamplerunitid,
               referencechannelno,
               expression,
               instancetype
        FROM tsl_monitorunitsignal
        WHERE stationId = #{stationId}
          AND equipmentId = #{equipmentId}
          AND signalId = #{signalId}
    </select>
    <select id="findSignalIdByEquipmentId" resultType="java.lang.Integer">
        SELECT signalid
        FROM tsl_monitorunitsignal
        WHERE EquipmentId = #{equipmentId}
    </select>
</mapper>