<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.siteweb.monitoring.mapper.SamplerUnitMapper">
    <select id="findSamplerUnit" resultType="com.siteweb.monitoring.entity.SamplerUnit">
        SELECT samplerUnit.SamplerUnitId, samplerUnit.SamplerUnitName
        FROM tsl_monitorunit monitorUnit
                 INNER JOIN tsl_samplerunit samplerUnit ON monitorUnit.MonitorUnitId = samplerUnit.MonitorUnitId
                 INNER JOIN tsl_port port ON port.PortId = samplerUnit.PortId
        WHERE monitorUnit.MonitorUnitId = #{monitorUnitId}
          AND samplerUnit.samplerUnitName = #{samplerUnitName}
          AND port.PortName = #{portName}
    </select>
    <select id="getSamplerUnitDTOByStationId" resultType="com.siteweb.monitoring.dto.SamplerUnitDTO">
        select a.MonitorUnitName, b.PortName, b.Setting, c.SamplerUnitName, c.ConnectState,c.DllPath,c.Id from tsl_monitorunit a
        inner join tsl_port b on a.MonitorUnitId = b.MonitorUnitId
        inner join tsl_samplerunit c on b.MonitorUnitId = c.MonitorUnitId and b.PortId = c.PortId
        where a.stationId = #{stationId}
    </select>

    <select id="findByEquipmentIds" resultType="com.siteweb.monitoring.entity.SamplerUnit">
        select d.* from tbl_equipment a
            inner join tsl_SamplerUnit d on d.MonitorUnitId = a.MonitorUnitId and d.SamplerUnitId = a.SamplerUnitId
        where a.EquipmentId in
        <foreach collection="equipmentIds" item="item" separator="," open="(" close=")">
            #{item}
        </foreach>
    </select>
</mapper>