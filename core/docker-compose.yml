version: "3.8"
services:
  mysql:
    container_name: siteweb-mysql
    image: mysql:8.0.22
    ports:
      - "3306:3306"
    volumes:
      - /etc/localtime:/etc/localtime
      - ./siteweb/mysql/db:/var/lib/mysql
      - ./siteweb/mysql/cnf:/etc/mysql/
    environment:
      TZ: "Asia/Shanghai"
      MYSQL_DATABASE: siteweb
      MYSQL_ROOT_PASSWORD: siteweb1!
      MYSQL_ROOT_HOST: '%'
      MYSQL_USER: siteweb
      MYSQL_PASSWORD: siteweb1!
      MYSQL_USER_HOST: '%'
    #    restart: always
    command: [
      "--character-set-server=utf8mb4",
      "--collation-server=utf8mb4_unicode_ci",
      "--lower_case_table_names=1",
      "--innodb_flush_log_at_trx_commit=1"
    ]
    cap_add:
      - SYS_NICE  # CAP_SYS_NICE
    security_opt:
      - seccomp:unconfined
    networks:
      - "monitoring"

  redis:
    image: redis:5.0.14
    container_name: siteweb-redis
    #    restart: always
    command: --appendonly no --requirepass siteweb1!
    ports:
      - 6379:6379
    volumes:
      - ./siteweb/redis:/data
    environment:
      TZ: "Asia/Shanghai"
    networks:
      - "monitoring"

  influxdb:
    container_name: siteweb-influxdb
    image: influxdb:2.1
    #  restart: always
    ports:
      - "8086:8086"
    environment:
      TZ: "Asia/Shanghai"
      DOCKER_INFLUXDB_INIT_MODE: setup
      #为管理员用户设置的用户名（必需）
      DOCKER_INFLUXDB_INIT_USERNAME: siteweb
      #为管理员用户设置的密码（必需）
      DOCKER_INFLUXDB_INIT_PASSWORD: siteweb@2021
      #为初始组织设置的名称（必需）
      DOCKER_INFLUXDB_INIT_ORG: siteweb
      #为初始存储桶设置的名称（必需）
      DOCKER_INFLUXDB_INIT_BUCKET: siteweb
      #为初始化存储桶应保留数据的持续时间
      DOCKER_INFLUXDB_INIT_RETENTION: 1095d
    volumes:
      - /etc/localtime:/etc/localtime
      - ./siteweb/influxdb/conf:/etc/influxdb2 #默认的配置文件  修改连接参数，重启即可
      - ./siteweb/influxdb/data:/var/lib/influxdb2 #数据文件
      - ./siteweb/influxdb/scripts:/docker-entrypoint-initdb.d #初始化脚本文件夹
    networks:
      - "monitoring"
    depends_on:
      - mysql

#  postgres:
#    image: postgres:12
#    #restart: always
#    privileged: true
#    container_name: siteweb-postgres
#    ports:
#      - 5432:5432
#    environment:
#      POSTGRES_USER: postgres
#      POSTGRES_PASSWORD: 123456
#      PGDATA: /var/lib/postgresql/data/pgdata
#    volumes:
#      - ./siteweb/postgres/data:/var/lib/postgresql/data/pgdata
#      #      - ./postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
#      - /etc/localtime:/etc/localtime
#    networks:
#      - "monitoring"

  frontend:
    container_name: siteweb-frontend
    image: vertiv/siteweb_frontend
    ports:
      - "5200:5200"
    restart: always
    environment:
      LANG: "en_US.UTF-8"
      TZ: "Asia/Shanghai"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./siteweb/nginx/config:/etc/nginx
      - ./siteweb/nginx/log:/var/log/nginx
      - ./siteweb/nginx/proxy:/var/lib/nginx/tmp/proxy
    networks:
      - "monitoring"
    depends_on:
      -  backend

  backend:
    container_name: siteweb-server
    image: vertiv/siteweb_backend
    ports:
      - "8200:8200"
    environment:
      LANG: "en_US.UTF-8"
      TZ: "Asia/Shanghai"
    restart: always
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./siteweb/backend/logs:/home/<USER>/logs #日志文件
      - ./siteweb/backend/upload-dir:/home/<USER>/upload-dir #日志文件
      - ./siteweb/backend/config:/home/<USER>/config:ro #日志文件
    networks:
      - "monitoring"
    depends_on:
      -  mysql
    tty: true

networks:
  monitoring:
    driver: bridge
    name: monitoring
