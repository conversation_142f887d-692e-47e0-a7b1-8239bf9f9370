common.year=year
common.month=month
common.day=day
common.hour=hour
common.minute=minute
common.seconds=seconds
utility.operationObjectType.insert=insert
utility.operationObjectType.update=update
utility.operationObjectType.delete=delete
admin.employeeName=user name
common.graphicPage.noDefaultGraphicPage=no default graphicPage
common.name=name
common.alias=alias
common.jobNumber=job number
common.employeeTitle=rank
common.gender=gender
common.contactOne=contact one
common.email=email
common.contactTwo=contact two
common.address=address
common.contactAddress=contact address
common.man=man
common.woman=woman
common.generalStaff=general staff
common.lead=lead
common.other=other
common.alarm.push=alarm push
common.notFound=not found
common.nameUnique=name unique

eventconvergence.convergencereason.poweroff=power off
eventconvergence.convergencereason.poweron=power on
eventconvergence.convergencereason.lacka=Phase voltage Ua phase loss 
eventconvergence.convergencereason.lackb=Phase voltage Ub phase loss
eventconvergence.convergencereason.lackc=Phase voltage Uc phase loss
eventconvergence.convergencereason.lackab=Phase voltage Ua,Ub phase loss
eventconvergence.convergencereason.lackbc=Phase voltage Ub,Uc phase loss
eventconvergence.convergencereason.lackac=Phase voltage Ua,Uc phase loss
eventconvergence.convergencereason.unkown=The reason is unknown 

eventNotification.activeEvent.equipmentName=equipment
eventNotification.activeEvent.eventName=alarm
eventNotification.activeEvent.eventSeverity=alarm severity
eventNotification.activeEvent.eventValue=event occur value
eventNotification.activeEvent.startTime=start time
eventNotification.activeEvent.endTime=end time
eventNotification.activeEvent.meanings=event meanings
eventNotification.activeEvent.reversalNum=reversal number
eventNotification.activeEvent.fullPosition=full position
eventNotification.activeEvent.concisePosition=concise position
eventNotification.start=start
eventNotification.end=end
eventNotification.getui.pushResponseCode.10001=token error/invalid
eventNotification.getui.pushResponseCode.10002=appId or ip is in the blacklist
eventNotification.getui.pushResponseCode.10003=authentication Interface calls are too frequent
eventNotification.getui.pushResponseCode.10004=permission denied
eventNotification.getui.pushResponseCode.10005=Interface calls are too frequent
eventNotification.safeMessage.safeEmail=safe email

login.captcha.build.error=Failed to generate verification code
login.captcha.code.notExist=The verification code does not exist
login.captcha.code.keyNotExist=The verification key does not exist
login.captcha.code.error=Verification code error
login.captcha.code.expiredOrNonexistent=Verification code error

# 报表公共字段
common.report.form.serialNo=serial number
common.report.form.eventSeverity=alarm severity
common.report.form.equipmentName=equipment name
common.report.form.equipmentState=equipment state
common.report.form.equipmentPosition=equipment position
common.report.form.regionPosition=region position
common.report.form.equipmentType=equipment type
common.report.form.eventDescription=event description
common.report.form.baseEquipmentName=base equipment name
common.report.form.eventName=event name
common.report.form.signalName=signal name
common.report.form.eventValue=event occur value
common.report.form.eventRemarks=event note
common.report.form.Comment=comment
common.report.form.startTime=start time
common.report.form.endTime=end time
common.report.form.confirmTime=confirm time
common.report.form.confirmerName=confirm person
common.report.form.confirmReason=confirm reason
common.report.form.duration=duration
common.report.form.operationTime=operation time
common.report.form.operatorName=operator person
common.report.form.operationContent=operation Content
common.report.form.endPerson=end the person
common.report.form.alarmOperatorName=shielding Operator
common.report.form.operationType=operation type
common.report.form.alarmStartTime=shielding start time
common.report.form.timeGroupCategory=time Group Category
common.report.form.timeGroupChars=time Group Chars
common.report.form.alarmEndTime=shielding end time
common.report.form.reason=end reason
common.report.form.roomName=room name
common.report.form.alarmCount=alarm count
common.report.form.current=current
common.report.form.history=history
common.report.form.oneLevelAlarm=one level alarm
common.report.form.currentOneLevelAlarm=current one level alarm
common.report.form.historyOneLevelAlarm=history one level alarm
common.report.form.twoLevelAlarm=two level alarm
common.report.form.currentTwoLevelAlarm=current two level alarm
common.report.form.historyTwoLevelAlarm=history two level alarm
common.report.form.threeLevelAlarm=three level alarm
common.report.form.currentThreeLevelAlarm=current three level alarm
common.report.form.historyThreeLevelAlarm=history three level alarm
common.report.form.fourLevelAlarm=four level alarm
common.report.form.currentFourLevelAlarm=current four level alarm
common.report.form.historyFourLevelAlarm=history four level alarm
common.report.form.controlName=control name
common.report.form.controlTime=control time
common.report.form.responseTime=response time
common.report.form.state=state
common.report.form.resourcePosition=resource name
common.report.form.ITDeviceName=it device name
common.report.form.rackName=rack name
common.report.form.uIndex=u index
common.report.form.customer=customer
common.report.form.business=business
common.report.form.purchaseDate=purchase date
common.report.form.changeDate=change date
common.report.data.max=Max
common.report.data.min=Min
common.report.data.avg=Avg
common.report.data.sum=Sum
common.report.data.first=First
common.report.data.last=Last
common.report.form.putInShelf=put in shelf
common.report.form.takeFormShelf=take form shelf
common.report.form.eventMeanings=event meanings
common.report.form.alarmTime=alarm time
common.report.form.receiver=receiver
common.report.form.sendTime=send time
common.report.form.sendContent=send content
common.report.form.sendType=send type
common.report.form.sendResult=send result
common.report.form.total=total
common.field.usedUIndex=Used UIndex
common.field.freeUIndex=Free UIndex
common.field.alreadyExists=already exists
common.field.cannotEmpty=cannot be empty
common.field.allEquipment=all equipment
common.field.success=success
common.field.unknownError=unknown error
common.field.notFindDevice=not find it device
common.field.notFindRack=not find rack
common.field.itDeviceNotInRack=it device not in rack
common.field.uIndexMoreZero=u index more than zero
common.field.itDeviceFree=it device must be idle
common.field.itDeviceOutRange=it device location out of range
common.field.uIndexEmpty=u index must be empty
common.field.shiftGroupNameUnique=shift group name exists
common.field.shiftGroupExists=user:%s,Existing shift group
common.field.expertAdviceExists=alarm base type:%s expert advice exists
common.field.site=Site
common.field.quantity=quantity
common.field.percentage=percentage
common.field.total=total
common.field.district=District
common.field.powerFailure=Power failure
common.field.electricityGeneration=Electricity generation
common.field.interrupt=Offline
common.field.rank=rank
common.field.monitorStatus=monitoring status
common.field.underMonitoring=under monitoring
common.field.blockedState=blocked state
common.field.engineeringState=engineering state
common.report.csv.position=position
common.report.csv.collectTime=collect time
common.report.csv.equipmentName=equipment name
common.report.csv.signalName=signal name
common.report.csv.signalValue=signal value
common.report.csv.originValue=origin value
common.report.csv.unit=unit
common.report.alarmCount=alarm count
common.report.form.equipmentCount=equipment count
common.report.form.allAlarm=all alarm
common.report.form.abnormalAlarm=abnormal alarm
common.report.form.abnormalHistoryAlarm=abnormal history alarm
common.report.form.abnormalActiveAlarm=abnormal active alarm
common.report.form.constructAlarm=construct alarm
common.report.form.constructHistoryAlarm=construct history alarm
common.report.form.constructActiveAlarm=construct active alarm
common.report.form.alarmProcessStatus=Alarm Processing Status
common.report.form.eventReasonTypeName=Alarm classification
common.report.form.unconfirmedUnrecovered=Unconfirmed & Unrecovered
common.report.form.unconfirmedRecovered=Unconfirmed & Recovered
common.report.form.confirmedUnrecovered=Confirmed & Unrecovered
common.report.form.confirmedRecovered=Confirmed & Recovered
common.report.form.alarmAnalysis=Alarm Analysis
common.report.form.responsiblePerson=Responsible Person
common.report.form.failureReason=Fault Reason
common.report.form.noFailureReason=No Fault Reason
common.report.form.deviceType=Device Category
common.report.form.proportion=Proportion
common.report.form.alarmDistribution=Alarm Distribution
common.report.form.alarmProportion=Alarm Proportion
common.report.form.alarmList=Alarm List
common.report.form.fileExportOperationError=File Export Operation Error

# 告警屏蔽操作类型
alarm.mask.operationType=[{"operationType":"1","desc":"add alarm"},{"operationType":"2","desc":"remove alarm"}]
alarm.mask.timeGroupCategory=[{"timeGroupCategory":"1","desc":"block all time"},{"timeGroupCategory":"2","desc":"block by time"}]
# 蓄电池
battery.cell.signal=%02d#single
battery.workstatus.discontinue=Discontinue
battery.workstatus.float=Float
battery.workstatus.discharge=Discharge
battery.workstatus.equalize=Equalize

energy.msg.elecfeeconfig.notcover24hours=The time period does not cover 24 hours a day
energy.msg.elecfeeconfig.fpghasoverlap=Overlapping of time periods
energy.msg.elecfeeconfig.structurehasoverlap=The following selected levels overlap with their existing schemes in [applicable months]:
energy.msg.elecfeeconfig.appliedrangenotnull=Applied range cannot be empty
energy.msg.elecfeeconfig.atleastonestep=At least one step pricing is required
energy.msg.dimension.complexindexexist=This type of complexindex already exists
energy.msg.dimension.defaultnodecannotoperate=The default node cannot be operated here
energy.msg.dimension.subtreenotexist=Subtree does not exist
energy.msg.dimension.newzero=This type of complexindex already exists or does not need to be added. 0 newly added.
energy.msg.dimension.rootnodeexists=Root node already exists
energy.msg.dimension.parentnodenotexist=Parent node does not exist
energy.msg.dimension.parentnodegreaterthanone=Parent node is greater than 1
energy.msg.dimension.nodenameexists=Node name already exists
energy.msg.dimension.defaultnodecannotedit=The default node cannot be edited here
energy.msg.dimension.deletenodenotexists=The node to be deleted does not exist
energy.msg.dimension.nodedupref=Node duplicate reference
energy.msg.dimension.cannotaddchild=This type of node cannot add child nodes
energy.msg.dimension.computerrackcannotaddracknode=Adding a rack node under a computer rack node is not allowed
energy.msg.dimension.computerrackcannotaddstructurenode=Adding a structure node is not allowed under the computer rack node
energy.msg.dimension.refequipmentnodecannotedit=Reference device is not editable
energy.msg.dimension.refcomputerracknodecannotedit=Reference computer rack is not editable
energy.msg.dimension.refitdevicenodecannotedit=Reference it devicen is not editable
energy.msg.dimension.objectNoFather=This node does not find a corresponding parent node
energy.msg.dimension.fatherValueLowChild=The current quota is less than the total amount of child nodes; the total amount of child nodes is:
energy.msg.dimension.objectOverTotal=The current node quota has exceeded the total amount, and the remaining amount is
energy.msg.dimension.InsertFailed=Insert failed
energy.msg.dimension.updateFailed=Update failed

energy.common.enable=Enable
energy.common.disable=Disable

energy.displaytext.carbon=CO2

energy.enum.month.m1=Jan
energy.enum.month.m2=Feb
energy.enum.month.m3=Mar
energy.enum.month.m4=Apr
energy.enum.month.m5=May
energy.enum.month.m6=Jun
energy.enum.month.m7=Jul
energy.enum.month.m8=Aug
energy.enum.month.m9=Sep
energy.enum.month.m10=Oct
energy.enum.month.m11=Nov
energy.enum.month.m12=Dec

energy.totalanalysis.total=total
energy.totalanalysis.TotalCoal=TotalCoal
energy.totalanalysis.formatString_1=yy-MM
energy.totalanalysis.formatString_2=M
energy.totalanalysis.formatString_3=d
energy.totalanalysis.formatString_4=H

# u位标签管理
udevice.utag.valueexists=tag unique value exist
udevice.utag.bindingrate=Tag Bind Rate
udevice.itdevice.notxists=itdevice not exist
udevice.utag.codeEmpty=Tag code cannot be empty
# license
license.message.licenseerror=license error,please check license
license.message.fileEmpty=license error,empty file
license.message.typeError=license error,file type error
# 公共提示
common.msg.idIsNotEmpty=Id Is Not Empty
common.msg.disallowOperationEmptyRecord=Disallow Operation Empty Record
common.msg.equipmentexists=A device with the same name already exists in the system. Please change the name and try again

common.timeJobMsg.addJobException=Create Time Job Exception
common.timeJobMsg.updateJobException=Update Time Job Exception
common.timeJobMsg.removeJobException=Remove Time Job Exception
common.field.signal=signal
common.field.complexIndex=complexIndex
common.field.alarmCount=Alarm Count
common.field.signalNotExist=Signal Not Exist
common.field.complexIndexNotExist=complexIndex Not Exist
#历史预警报表
prealarm.report.form.preAlarmSeverity=PreAlarm Severity
prealarm.report.form.levelOfPath=LevelOfPath
prealarm.report.form.objectName=ObjectName
prealarm.report.form.preAlarmName=PreAlarm Name
prealarm.report.form.preAlarmCategory=PreAlarm Category
prealarm.report.form.triggerValue=Trigger Value
prealarm.report.form.startTime=StartTime
prealarm.report.form.confirmTime=ConfirmTime
prealarm.report.form.endTime=EndTime
prealarm.report.form.confirmName=Confirmer

userOpLog.report.loginName=login Name
userOpLog.report.userName=userName
userOpLog.report.employee=employee
userOpLog.report.jobNumber=jobNumber
userOpLog.report.operatingContent=operating Content
userOpLog.report.operatingTime=operating Time

utility.sms.code.template = [SiteWeb6]Sms verification code:%s, valid for 10 minutes!
account.login.timeSpan.sms.template = [SiteWeb6]UserName:%s, To access the system during non-access hours, please note!
login.ip.filterpolicy.sms.template = [SiteWeb6]IP:%s, To access the system during non-access hours, please note!

api.stationStatus.0=offline
api.stationStatus.1=online
api.stationStatus.2=unregistered

api.stationStatus.offlineCount=Offline number
api.stationStatus.onlineCount=Online number
api.stationStatus.activeEventCount=Active event count

audit.report.addDepartment=Add Department
audit.report.deleteDepartment=Delete Department
audit.report.updateDepartment=Update Department
audit.report.addEmployee=Add Employee
audit.report.deleteEmployee=Delete Employee
audit.report.updateEmployee=Update Employee
audit.report.updatePassword=Update Password
audit.report.resetPassword=Reset Password
audit.report.setUserTheme=Eet User Theme
audit.report.addAccount=Add Account
audit.report.updateAccount=Update Account
audit.report.startStopAccount=start Stop Account
audit.report.deleteAccount=Delete Account
audit.report.deleteRole=Delete Role
audit.report.updateRole=Update Role
audit.report.addRole=Add Role
audit.report.addRegionPermission=Add Region Permission
audit.report.updateRegionPermission=Update Region Permission
audit.report.deleteRegionPermission=Delete Region Permission
audit.report.login=Login Success
audit.report.logout=Logout Success
audit.report.confirmAlarm=Confirm Alarm
audit.report.cancelAlarm=Cancel Alarm
audit.report.alarmNote=Alarm Note
audit.report.confirmControlCommand=Confirm Control Command
audit.report.reSendControlCommand=Re Send Control Command
audit.report.sendControlCommand=Send Control Command
audit.report.signalSetting=Signal Setting
audit.report.eventSetting=Event Setting
audit.report.addReport=Add Report
audit.report.deleteReport=Delete Report
audit.report.updateReport=Update Report
audit.report.addVisitTimeSetting=Add Visit Time Setting
audit.report.updateVisitTimeSetting=Update Visit Time Setting
audit.report.deleteVisitTimeSetting=Delete Visit Time Setting
audit.report.operationAccount=Operation Person
audit.report.auditLevel=Audit Level
audit.report.clientIp=ClientIp
audit.report.content=Content
audit.report.eventResult=Event Result
audit.report.eventType=Event Type
audit.report.operationTime=Operation Time
audit.report.loginSetting=Update Security Setting，Login Setting.
audit.report.auditSetting=Update Security Setting，Audit Setting.
audit.report.historyPasswordCount=history Password Not Repeated Count:
audit.report.infinite=Infinite
audit.report.one=One
audit.report.loginFreezeTime=Login FreezeTime:%s(s)
audit.report.loginTryDuration=Login Try Duration：%s(m)
audit.report.maxConcurrentUserCount=Max Concurrent User Count:
audit.report.userVisitTimespanSetting=user Visit Timespan Setting:
audit.report.active=active
audit.report.close=close
audit.report.ipVisitSetting=Ip Visit Setting:
audit.report.enableAuditReport=Enable Audit Report:
audit.report.open=Open
audit.report.auditReportLevel=Audit Report Level:
audit.report.minLevel=Min Level
audit.report.basicLevel=Basic Level
audit.report.detailLevel=Detail Level
audit.report.notStated=Not Stated
audit.report.passwordError=password error
audit.report.operationSuccess=operation success
audit.report.operationFail=operation fail
audit.report.securitySettingModule=security setting module
audit.report.useraccountfreeze=User account is freeze
audit.report.volenthack=Please note for brute force login!


security.report.operationAccount=Operation Person
security.report.type=Type
security.report.clientIp=Client Ip
security.report.details=Details
security.report.operationTime=Operation Time

projectstate.invalidparam=Params are invalid
projectstate.addequipmentsate=Add New Equipment Project Appointment
projectstate.uptequipmentsate=Edit  Equipment Project Appointment
projectstate.delequipmentsate=Delete  Equipment Project Appointment
projectstate.maskconflict=Conflict Between Project Period and Mask Period

projectstate.addstationprojectstate=Add New Station Project Appointment
projectstate.uptstationprojectstate=Edit Station Project Appointment
projectstate.delstationprojectstate=Delete Station Project Appointment
projectstate.success=Project status set successfully
projectstate.equipment.addOperationLog=%s set project status time from %s to %s
projectstate.equipment.deleteOperationLog=%s delete project status
projectstate.text=Project State

projectstate.addhouseprojectstate=Add New House Project Appointment
projectstate.upthouseprojectstate=Edit House Project Appointment
projectstate.delhouseprojectstate=Delete House Project Appointment

nets.operation.group.name=Action Permission group
nets.operation.group.Description=Action Permission

security.file.integrity.log=[SiteWeb6]File integrity self-checking list：%s Unsuccessful!

airconditioncontrol.common.manageEquipName=AirConditioningManageEquipment
airconditioncontrol.common.manageEquipTemplateName=AirconEnergySaving
airconditioncontrol.common.manageGroupName=Group
airconditioncontrol.common.reloadConfigControlName=Reload Config
airconditioncontrol.common.stdAirType.common=General AirConditioner
airconditioncontrol.common.stdAirType.special=Special AirConditioner
airconditioncontrol.common.stdAirType.common2=OneDrivenTwo AirConditioners
airconditioncontrol.common.new=New
airconditioncontrol.common.update=Update
airconditioncontrol.common.delete=Delete

signature.invalid=[SiteWeb6]Anti-Replay Attack:Signature is invalid
signature.Expired=[SiteWeb6]Anti-Replay Attack:Signature is out of date
notification.gateway.access.success=Notify the gateway of successful access
notification.gateway.access.failure=Notify gateway of access failure



airConditionControl.view.airStateOn=On
airConditionControl.view.airStateOff=Off
airConditionControl.view.signalValueInfoUnKnow=UnKnow
airConditionControl.view.airStateInfoOff=Device Status : Off
airConditionControl.view.airStateInfoOn=Device Status : On
airConditionControl.view.airStateInfoUnKnow=Device Status : UnKnow
airConditionControl.view.noSignalMap=The device does not map a signal
airConditionControl.view.signalNameTemp=temperature
airConditionControl.view.signalNameStateOnOrOff=Device Status
airConditionControl.view.signalNameEnvironmentTemp=Ambient Temperature
airConditionControl.view.signalNameAirTemp=Dedicated Air Conditioning Temperature
airConditionControl.view.signalNameAir1Temp=Air conditioner 1 temperature
airConditionControl.view.signalNameAir2Temp=Air conditioner 2 temperature
airConditionControl.view.air=Air conditioner
airConditionControl.view.groupStateRunning=Running
airConditionControl.view.groupStateStop=Stop

airConditionControl.autoControl.group=Group
airConditionControl.autoControl.basicParam=Basic Params
airConditionControl.autoControl.mapTemperature=Temperature Equipment
airConditionControl.autoControl.mapFan=Fan Equipment
airConditionControl.autoControl.mapZoneStrategy=Zone Strategy
airConditionControl.autoControl.mapZoneStrategy.scheme=Scheme
airConditionControl.autoControl.mapZoneStrategy.operation=Operation
airConditionControl.autoControl.mapEnergyComplexIndex=Energy ComplexIndex
airConditionControl.batchControl.controlResult.success=Success
airConditionControl.batchControl.controlResult.fail=Fail
airConditionControl.batchControl.controlResult.timeOut=Timeout
airConditionControl.batchControl.controlResult.sending=Sending

airConditionControl.report.common.operator=Operator
airConditionControl.report.common.groupName=Group Name
airConditionControl.report.common.changeType=Change Type
airConditionControl.report.common.detail=Detail
airConditionControl.report.common.insertTime=Record Time
airConditionControl.report.common.operateTime=Operate Time
airConditionControl.report.common.changeTime=Change Time
airConditionControl.report.common.stationName=Station Name
airConditionControl.report.common.houseName=House Name
airConditionControl.report.common.monitorUnitName=MonitorUnit Name
airConditionControl.report.common.equipmentName=Equipment Name
airConditionControl.report.common.operateModule=Operate Module

airConditionControl.report.batchControlCmdHistory.stdControlCmd=Standard Control Command
airConditionControl.report.batchControlCmdHistory.controlCmd=Control Command
airConditionControl.report.batchControlCmdHistory.controlParam=Command Param
airConditionControl.report.batchControlCmdHistory.stdAirconType=Standard Aircon Type
airConditionControl.report.batchControlCmdHistory.controlResult=Control Result
airConditionControl.report.batchControlCmdHistory.cmd.remoteStart=Remote Boot
airConditionControl.report.batchControlCmdHistory.cmd.remoteStop=Remote Shutdown
airConditionControl.report.batchControlCmdHistory.cmd.temperatureSet=Temperature Setting
airConditionControl.report.batchControlCmdHistory.cmd.heatMode=Switch Heating Mode
airConditionControl.report.batchControlCmdHistory.cmd.coolMode=Switch Cooling Mode

airConditionControl.report.autoControlEquipmentChangeLog.subChangeType=Change SubType
airConditionControl.report.autoControlEquipmentChangeLog.subObjectType=SubObject

# 空调设备历史运行状态报表
airConditionControl.report.equipStateForm.station=Station
airConditionControl.report.equipStateForm.monitorUnit=MonitorUnit Name
airConditionControl.report.equipStateForm.virtualEquipment=Virtual Equipment Name
airConditionControl.report.equipStateForm.equipment=Equipment
airConditionControl.report.equipStateForm.state=Run State
airConditionControl.report.equipStateForm.time=Time
airConditionControl.report.equipStateForm.stateNum=State Value

user.account.expired=[SiteWeb6]User account %s has expired
user.password.expired=[SiteWeb6]User %s password has expired!
user.account.locked=[SiteWeb6]User account %s is locked!
user.disabled=[SiteWeb6]User %s is not enabled!

#工作流
workflow.schedule.exist=This shift arrangement already exists

send.type.all=
send.type.sms=Short Message
send.type.mail=Mail
send.type.App=APP Notify
send.type.alarmbox=Alarm Box
send.type.alarmLight=Alarm Light
send.type.telephonevoice=Telephone Voice(SMS)
send.type.weComApply=WeCom Apply Notify
send.type.lark=Lark Notify
send.mail.subject=Alarm notification
send.result.success=Send Success
send.result.fail=Send Fail

asset.assetCode.exist=Asset Code already exists

# Ba控制命令报表
baControlCommand.report.equipmentId=Equipment Id
baControlCommand.report.equipmentName=Equipment Name
baControlCommand.report.controlValue=Control Value


# 配电
powerDistribution.invalidRequestMissingParameter=Invalid Request Missing Parameter
powerDistribution.noDistributionMapFound=No Distribution Map Found
powerDistribution.istributionMapNotConfigured=Distribution Map is Not Configured for Capacity or Power-On Base Class
powerDistribution.noElementFound=No Element Found
powerDistribution.requestedObjectNotDevice=Requested Object is Not a Device

# 通知策略
common.field.alarmNotifyGatewayServiceUnique= Alarm Notify Gateway Service URL exists
common.field.alarmNotifyGatewayServiceInUse=Alarm Notify Gateway Service URL In Use, Unable To Delete Or Update

# 门禁管理
accessControl.authorization.tooMuch=Door card authorizations must be 2000 or less.

# IT设备
common.field.inShelfItDeviceDelFail=ITDevices In Shelf, Unable To Delete

# 批量工具
virtualEquipment.notExist.virtualSignal=The virtual device signal does not exist
virtualEquipment.notExist.originChannel=The source device channel number does not exist
virtualEquipment.notExist.virtualEquipmentStation=The virtual device office does not exist
virtualEquipment.notExist.virtualMonitUnit=The virtual monitoring unit does not exist
virtualEquipment.notExist.sampleUnit=The virtual acquisition unit does not exist
virtualEquipment.notExist.virtualEquipmentHouse=The virtual device office does not exist
virtualEquipment.notExist.virtualTemplate=The virtual template does not exist
virtualEquipment.notExist.virtualEquipmentCategory=The virtual device type does not exist
virtualEquipment.notExist.originEquipment=The source device does not exist
virtualEquipment.cannotAcross.monitUnit=Virtual device and source devices cannot span monitoring units

# 指标
complexIndex.errorCode.1=Please check whether the signal value is a number or the divisor is zero!
complexIndex.errorCode.2=Please check if the expression is correct!

# 报表分类名称
report.category.name.1=Alarm
report.category.name.2=History
report.category.name.3=Complex Index
report.category.name.4=Log
report.category.name.5=Other
report.category.name.6=Battery
report.category.name.7=Security
report.category.name.8=Time Report

# 江苏运维平台
js.floor.MeterCount=Floor Meter Count
js.power.DeviceCount=Power Device Count
js.battery.Count=Battery Count
js.environmental.deviceCount=Environmental Device Count
js.powerOutage.alarmCount=Power Outage Alarm
js.lowOutputVoltage.alarmCount=Low Output Voltage Alarm
js.totalVoltage.alarmCount=Total Voltage Alarm
js.highTemperature.alarmCount=High Temperature Alarm
js.totalVoltage.tooLow=Total Voltage Of The Battery Pack Too Low
js.battery.failure=Battery Failure
js.battery.lowVoltageAlarm=During this period, a battery low voltage alarm is generated, and it is recommended to check the battery performance.
js.battery.packTooLowAlarm=The total voltage of the battery pack is too low
js.batterySupply.packTooLowAlarm=During this period, there were alarms for both power supply and low battery voltage. It is recommended to check if the power supply and battery performance are normal.
js.batterySupply.lostProvide=The power supply has lost its ability to provide power.
js.batterySupply.lostPartProvide=The power supply has lost its ability to part provide power.
js.powerSupply.normal=During this period, there was a power supply alarm. It is recommended to check if the power supply is functioning normally.
js.powerSupply.abnormalLoadCurrentAlarm=Abnormal total load current alarm
js.powerSupply.lowOutPutVoltageAlarm=Low DC output voltage alarm

#网络拓扑图
workStation.netWorkTopology.cpu=CPU(%%):%s
workStation.netWorkTopology.memory=Memory(M):%s
workStation.netWorkTopology.threads=Thread Count:%s
workStation.netWorkTopology.diskFree=Disk Remaining(GB):%s

#控制命令状态
command.status.success=Control Success
command.status.failure=Control Failure
command.status.timeout=Process Timeout
command.status.noResponse=Return Failure
command.status.addressError=Control Unit Address Error
command.status.parameterError=Parameter Error
command.status.queueTimeout=Timeout in the Control Queue
command.status.recordingEnded=Record Over
command.status.equipmentOffline=Equipment offline, control command sending failed

#屏蔽
shield.centerName=Center Name
shield.stationName=Station Name
shield.equipmentName=Equipment Name
shield.userName=Mask User Name
shield.startTime=Start Time
shield.endTime=End Time
shield.shieldTypeName=Mask Method
shield.description=Mask Remark
shield.maskMethod.1=Full Time Mask
shield.maskMethod.2=Time Period Mask

#版本管理 FSU导出
version.fsu.siteName=Site Name
version.fsu.fsuType=Collector Model
version.fsu.cpuUsage=CPU Usage (%)
version.fsu.memUsage=Memory Usage (%)
version.fsu.flashUsedRate=Flash Usage (%)
version.fsu.hw=Hardware Version
version.fsu.sn=SN Code
version.fsu.mac=MAC Address
version.fsu.ip=IP Address
version.fsu.memTotal=Memory Configuration
version.fsu.flashSize=Flash Configuration
version.fsu.linux=System Version
version.fsu.siteVersion=Site Unit Version
version.fsu.collectTime=Refresh Time

version.contract.projectName=Project Name
version.contract.contractNo=Contract Number
version.contract.installTime=Installation Date
version.contract.houseName=House Name
version.contract.equipmentName=Equipment Name
version.contract.FsuName=FSU Name
version.contract.FsuIp=FSU IP
version.contract.stationCount=Station Count
version.contract.fsuCount=FSU Count
version.contract.equipmentCount=Equipment Count
version.contract.primaryDate=Initial Acceptance Date
version.contract.endDate=Final Acceptance Date
version.contract.qualityStartPoint=Quality Assurance Start Point
version.contract.qualityPeriod=Quality Assurance Period (Months)
version.contract.qualityTerms=Quality Terms
version.contract.maintenanceStartDate=Maintenance Start Date
version.contract.maintenanceEndDate=Maintenance End Date
version.contract.maintenanceTerms=Maintenance Terms

#资产导入
asset.import.assettypeid.invalid=Invalid asset type fill
asset.import.assetstateid.invalid=Invalid asset state fill
asset.import.useddate.invalid=Invalid used date fill

# 自动巡检导出
patrol.alarm.centerName=Monitoring Name
patrol.alarm.groupName=Administrative Division
patrol.alarm.stationName=Station Name
patrol.alarm.equipmentCategoryName=Equipment Category Name
patrol.alarm.equipmentId=Equipment ID
patrol.alarm.equipmentName=Equipment Name
patrol.alarm.signalName=Signal Name
patrol.alarm.signalValue=Signal Value
patrol.alarm.recordTime=Collection Time
patrol.alarm.unit=Unit
patrol.alarm.byPercentageMeaning=By Percentage Meaning
patrol.alarm.ratedValue=Rated Value
patrol.alarm.warningMeaning=Warning Level
patrol.alarm.isPowerOffAlarm=Power Off Alarm Cause
patrol.alarm.createTime=Creation Time
patrol.alarm.limitMeaning=Matched Condition

#资产列表导出
assetdevice.assetCode=asset Code
assetdevice.assetName=name
assetdevice.assetCategoryName=type
assetdevice.brand=brand
assetdevice.model=model
assetdevice.capacityParameter=Capacity parameters
assetdevice.settingPosition=Installation location
assetdevice.serialNumber=Serial number
assetdevice.manufactor=factory


complexindex.complexIndexId=Index ID
complexindex.complexIndexName=Index Name
complexindex.objectId=Object ID
complexindex.calcCron=Calculation Period
complexindex.calcType=Is Difference Value
complexindex.saveCron=Save Period
complexindex.expression=Expression
complexindex.unit=Unit
complexindex.accuracy=Precision
complexindex.objectName=Object Name
complexindex.complexIndexDefinitionId=Index Definition ID
complexindex.businessTypeId=Business Type ID
complexindex.checkExpression=Check Expression
complexindex.businessTypeName=Business Type Name
common.report.form.position=position
common.report.form.monitorUnitName=monitor unit name
common.report.form.serialPort=serial port name
common.report.form.serialPortType=serial port type
common.report.form.DeviceProtocolLibrary=device protocol library
common.report.form.equipmentAddress=device address
common.report.form.serialPortParameters=serial port parameters
common.report.form.updateTime=update time

common.yesOrNo.1=Yes
common.yesOrNo.0=No
common.eventLevel.1=One Level Alarm
common.eventLevel.2=Two Level Alarm
common.eventLevel.3=Three Level Alarm
common.eventLevel.4=Four Level Alarm
common.guardian.sendControl=Operator: %s, Send under Reviewer: %s
common.guardian.controlMeanings=Control meaning：%s

common.areaGroup=Area Group

equipment.manage.status.signalpoint = Signal Point Count
equipment.manage.status.connectstatus = Connection Status
equipment.manage.status.alarmstatus = Alarm Staus
equipment.manage.status.alarmstatus.normal = Normal
equipment.manage.status.point.index = Index
equipment.manage.status.point.name = Measuring point name
equipment.manage.status.point.signalid = Parameter ID
equipment.manage.status.point.channelno = Parameter number
equipment.manage.status.point.loopno = Loop number

search.it.device.title=Search IT Device

# Chart related text
report.chartImageGenerator.axis.time=Time
report.chartImageGenerator.axis.value=Value
report.chartImageGenerator.date.format=Date: {0}

# Battery chart related text
report.chartImageGenerator.battery.voltage=Voltage
report.chartImageGenerator.battery.current=Current
report.chartImageGenerator.battery.capacity=Capacity (mAh)
report.chartImageGenerator.battery.voltage.unit=Voltage (V)
report.chartImageGenerator.battery.current.unit=Current (A)
report.chartImageGenerator.battery.temperature.unit=Temperature (°C)

# Chart types
report.chartImageGenerator.type.voltage=Discharge Voltage
report.chartImageGenerator.type.current=Discharge Current
report.chartImageGenerator.type.voltage.vs.capacity=Voltage vs Capacity

# BatteryDischargeSheetCreator related text
report.batteryDischargeSheetCreator.data=Data
report.batteryDischargeSheetCreator.dischargeData=Discharge Data
report.batteryDischargeSheetCreator.timeRange=Time Range
report.batteryDischargeSheetCreator.name=Name
report.batteryDischargeSheetCreator.totalVoltage=Total Voltage
report.batteryDischargeSheetCreator.totalCurrent=Total Current
report.batteryDischargeSheetCreator.dischargedCapacity=Discharged Capacity
report.batteryDischargeSheetCreator.totalCapacity=Total Capacity
report.batteryDischargeSheetCreator.remainingCapacityRate=Remaining Capacity Rate
report.batteryDischargeSheetCreator.cell=Cell
report.batteryDischargeSheetCreator.voltage=Voltage
report.batteryDischargeSheetCreator.current=Current
report.batteryDischargeSheetCreator.capacity=Capacity
report.batteryDischargeSheetCreator.temperature=Temperature
report.batteryDischargeSheetCreator.time=Time
report.batteryDischargeSheetCreator.dischargeCharts=Discharge Charts
report.batteryDischargeSheetCreator.dischargeVoltage=Discharge Voltage
report.batteryDischargeSheetCreator.dischargeCurrent=Discharge Current
report.batteryDischargeSheetCreator.cellVoltage=Cell Voltage
report.batteryDischargeSheetCreator.cellTemperature=Cell Temperature

common.report.form.resourceStructureName=Hierarchy Name
common.report.form.equipmentCategory=Equipment Type
common.report.form.ipAddress=IP Address
common.report.form.samplerUnitName=Collector Unit Name
common.report.form.address=Collector Unit Address
common.report.form.equipmentTemplateName=Equipment Template Name
common.report.form.resourceStructureId=ResourceStructure ID
common.report.form.equipmentId=Equipment ID
common.report.form.stationId=Station ID
common.report.form.stationName=Station Name
common.report.form.monitorUnitId=Monitor Unit ID

# Complex Index Dependency Validation
complexIndex.dependency.indexIdCannotEmpty=Index ID cannot be empty
complexIndex.dependency.invalidFormatMustBeNumber=Invalid format, must be a number
complexIndex.dependency.indexIdValidationPassed=Index calculation order validation passed

