spring:
  datasource:
    url: ****************************************************************************************************************************************************************************************************
    username: root
    password: Vertiv@086
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      pool-name: Retail_HikariCP
      minimum-idle: 5
      idle-timeout: 180000
      maximum-pool-size: 200
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  main:
    banner-mode: console
  mvc:
    static-path-pattern: /**
    pathmatch:
      matching-strategy: ant_path_matcher
    date-format: yyyy-MM-dd HH:mm:ss
  web:
    locale: zh_CN
    locale-resolver: fixed
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB
  messages:
    basename: i18n/messages
    cache-duration: -1s
    fallback-to-system-locale: false
  resources:
    static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,classpath:/webui/

  liquibase:
    change-log: classpath:db/changelog/db.mysql.changelog-master.xml
    enabled: false
  redis:
    database: 0
    host: siteweb.redis
    port: 6379
    password: siteweb1!
    jedis:
      pool:
        max-active: 200
        max-wait: -1
        max-idle: 10
        min-idle: 0
      timeout: 1000
  influx:
    url: http://siteweb.influxdb:8086
    user: admin
    password: adminadmin
    database: siteweb_v2 #siteweb映射库，兼容1.x
    database2: siteweb5m_v2 #siteweb5m映射库，兼容1.x
    database3: sitewebenergy_v2 #siteweb能耗数据映射库，兼容1.x
  data:
    mongodb:
      database: mymongo
      port: 27017
      host: siteweb.mongo
  quartz:
    scheduler-name: clusteredScheduler # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: never # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。
    auto-startup: true # Quartz 是否自动启动
    startup-delay: 0 # 延迟 N 秒启动
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    overwrite-existing-jobs: false # 是否覆盖已有 Job 的配置
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          dataSource:
          #provider: hikaricp
          # JobStore 相关配置
          jobStore:
            # 数据源名称
            dataSource: quartzDataSource # 使用的数据源
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore # JobStore 实现类
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate
            tablePrefix: QRTZ_ # Quartz 表前缀
            isClustered: true # 是集群模式
            clusterCheckinInterval: 1000
            useProperties: false
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级

  # AD域认证相关配置
  ldap:
    urls: ldap://*************:389
    base: OU=test1,OU=test,OU=VET,DC=center,DC=com
    username: <EMAIL>
    password: 123456

  # 邮件服务相关配置
  #  mail:
  #    host: smtp.test.com
  #    port: 465
  #    username: <EMAIL>
  #    password: 123456789
  #    protocol: smtp
  #    defaultEncoding: UTF-8
  #    properties:
  #      mail:
  #        smtp:
  #          ssl:
  #            enable: true
  #          auth: true
  #          starttls:
  #            enable: true
  #            required: true
  #          socketFactory:
  #            port: 465
  #            class: javax.net.ssl.SSLSocketFactory
  #            fallback: false
  security:
    # 启用防重放攻击
    antiReplayAttack: false
    # 签名验证超时时间(分钟)
    signTimeout: 1
    # 允许未签名访问的url地址
    ignoreSignUri:
      - /swagger-ui.html
      - /swagger-resources
      - /v2/api-docs
      - /webjars/springfox-swagger-ui
      - /websocket
      - /doc.html
      - /csrf
      - /login
      - /error
      - /api/serverstatus
      - /api/checklicense
      - /api/files
      - /api/timestamp
      - /api/sso/login
      - /api/sso/logout
      - /api/keycloak/login
      - /api/fileintergrity/status
      - /api/monitorunit/register

mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    banner: false
  configuration:
    database-id: mysql
    mapUnderscoreToCamelCase: false
logging:
  config: classpath:logback-spring.xml

#security:
#  ignored: /**
#  basic:
#    enabled: false


jwt:
  privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/DZ8QrtQaoyFwO/fBno3LguBv2CWsUqJlCjh0opI3jrDnI8bTIRGotKLrknT0GSEXw5CtakEAa1C8tudwJ5nL09UAvQXRAgH23d/P6HSQVjDwh/2VIf9j9hh6meJGH/7xFr4HRMyB/cVHgqwvmiCNZIbvCWbyijcTXr0VPkPzL4sWF2pgdvJZzmgKkAz0xcOHqUfqp5YsyEIRovX8xmp2qcBzvpxHZYkaYujv/cO7FNc9bOA6dhh5ayZu3MeAU313g72nqt/nA4yTWq27d0zkKfdy17ZhKA+vYvLJKlrv+8tO5UDpIpB0vQhKd7+S2w9r7tHVGfezUWCZgKxZ/LL3AgMBAAECggEBALK1FRLP1crMyJxpG4javJueYj18G1EjQo/sjX5cCxU4vbSXPIWEqzX5MWPU7NzfHJtT7OKpPwAbYbwEAlxgTnXgQZ+dL/GfRSMbyxx4vX+9f62eJs72rCNesOsNQiCCEUCGG15FNl5pd706N8GXE9fuLmEtlEROkNHnjkpuobS4JWhGGJci4mNQqw4ZgCOuUr5YZ4QA/Mb6BBAu6bcvu6es23oGB84ZcxpMjh69PSwfiYFFsppuiJflzjQjohtyv99MqstDRvrzPi+i/T3qZ2W9FRUuP1pf/dMaamg7PoTAoOgJjKbSo7aEgLZIo/SF7TKnjtB6SmBnlWxi7HLyKyECgYEA+PP0CbhnkcVolsCQtLwa4lL/3mOurjjtgCWYXBkYjbZ9vJgGWw2oJ+HUBNhpEa7ELrzjUvXumgqCnicDeUC/9QJDCf029ldGje9GkrHsOSbU6IHzzdGbmYHwgfnjB6vFwZx5VjvT2t3VBMYeet5HqrL33KD+WxRYziel6URSbKkCgYEAxHYYcw0ORtq57g8N7SakScNE2oVf+/1H3WqSUJDg+T38WEkxU9M6sqnJ9q2v9QSR2QynxS0SZKxvwJc4LzUbiHMP7v20xhBrA5+a8kzjPqKlM50p0C/Nkt7pSwFiX+LwFQlMUV9tWdY/Z6fBhKks9uxpEPuCbCjDlCg0T0iNRp8CgYEA7WinlwV2LztUrD7jQJgKAz8npsrk8Fx1kTlI/LsqASrA6bMIjJiPfckMSbqfKC/EAtY66wiBDAFt4qhN1bn71QjdKY+CdJVyQTSn1ok6Pp5bd4dGG0cC3fdehnTpHo2evy4bQDM5q4TU+gJ9WqrTKWQWnx4gsnbK4X5J6BQxjlECgYBxtZi5Hplg0UBEVVpOJMt6FhdIE2JWy2ZI9WHyV6ifGg1wXAy848lZl4RZznXFbvurkPOZ4FiBBH06D0xppmdlNpPGU/nJmb8Wvc5E59OvcRwFH7YP1Vs64uJMk2SI8yTaSCNwBbeZA7R3HlWXnwNzd6noNmpqh72LhymfqfJ7KQKBgEjDxUq7q3KU0KeYiitT3WGOHQ5kGmfEwNFjuxMqJohyyJ4Z3ajHbIbrm2TZ5Wq3Ims+XcQFiOhZ8UIIUiOldjgFPlL5sBWLyh2Q0MO8lvBH+AJVAsirmNSTHzCIcvGDLEHnppSnmBOtC89h6n8Hdcz2Ybd1kVuYzrVXBnodakL0
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2fEK7UGqMhcDv3wZ6Ny4Lgb9glrFKiZQo4dKKSN46w5yPG0yERqLSi65J09BkhF8OQrWpBAGtQvLbncCeZy9PVAL0F0QIB9t3fz+h0kFYw8If9lSH/Y/YYepniRh/+8Ra+B0TMgf3FR4KsL5ogjWSG7wlm8oo3E169FT5D8y+LFhdqYHbyWc5oCpAM9MXDh6lH6qeWLMhCEaL1/MZqdqnAc76cR2WJGmLo7/3DuxTXPWzgOnYYeWsmbtzHgFN9d4O9p6rf5wOMk1qtu3dM5Cn3cte2YSgPr2LyySpa7/vLTuVA6SKQdL0ISne/ktsPa+7R1Rn3s1FgmYCsWfyy9wIDAQAB

server:
  http:
    port: 8200
  port: 8300
  ssl:
    enable: false
    key-store-password: siteweb1!
    key-store: classpath:s6-server.p12
    key-store-type: JKS
    key-alias: s6-server
    ciphers:  TLS_AES_256_GCM_SHA384,TLS_CHACHA20_POLY1305_SHA256,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
    protocol: TLS
    enabled-protocols: TLSv1.2
    client-auth: need
    trust-store: classpath:trustKeys.p12
    trust-store-password: siteweb1!
    trust-store-type: JKS

knife4j:
  enable: true
  production: false

fileserver:
  rootPath: upload-dir

# cas login configuration
cas:
  serverName: http://*************:4200
  loginServer: http://*************:4200/cas
  validateServer: http://*************:4200/cas

aes-key: lmGMlJPZQBpcx3Wf

sensitive:
  enableFastFilter: true
  enableJackFilter: true
  enableUndoFilter: true

#app.json容器内映射路径
appjsonfile: /home/<USER>/assets/json/app.json