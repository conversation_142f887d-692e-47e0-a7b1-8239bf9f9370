<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_complexIndexFunction_info" author="wxc">
        <createTable tableName="ComplexIndexFunction">
            <column name="FunctionId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="FunctionExpression" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="FunctionDescription" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="FunctionName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Remark" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ComplexIndexFunction" columnName="FunctionId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>