<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="gene_oillevelrecord" author="lz">
        <createTable tableName="gene_oillevelrecord" remarks="gene oilLevel">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="OilId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="CurrentOilLevel" type="Double">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateUserId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="gene_oillevelrecord" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="gene_oillevelrecord" indexName="IDX_Gene_OilLevel_1">
            <column name="OilId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>