<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_tblDriverTemplate_info" author="leizhiyi">
        <!-- 驱动模板 -->
        <createTable tableName="tbl_drivetemplate" remarks="tbl_drivetemplate info table (驱动模板表)">
            <column name="Id" type="int" remarks="驱动模板自增id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="driveTemplateName" type="varchar(100)" remarks="驱动模板名称">
                <constraints nullable="false"/>
            </column>
            <column name="driverTemplateDescribe" type="varchar(255)" remarks="驱动模板描述">
                <constraints nullable="true"/>
            </column>
            <column name="isDefaultTemplate" type="tinyint(1)" remarks="是否是默认模板(当前模板类型下)" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="driveTemplateType" type="int" remarks="驱动模板类型">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="tbl_drivetemplate" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>