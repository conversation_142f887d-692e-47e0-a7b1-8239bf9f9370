<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tbl_PatrolStandardGroup_info" author="ChenPeiHong"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="tbl_PatrolStandardGroup" remarks="tbl_PatrolStandardGroup info table">
            <column name="GroupId" type="int" remarks="分组id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GroupName" type="varchar(128)" remarks="分组名">
                <constraints nullable="false" />
            </column>
            <column name="EquipmentLogicClassId" type="int" remarks="标准化设备id">
                <constraints nullable="true"/>
            </column>
            <column name="StandardDicId" type="int" remarks="标准化Id">
                <constraints nullable="true"/>
            </column>
            <column name="Note" type="varchar(255)" remarks="备注">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="tbl_PatrolStandardGroup" columnName="GroupId" incrementBy="1" columnDataType="int" startWith="1"/>

    </changeSet>
</databaseChangeLog>