<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="gene_generatorsignalcheckinfo" author="lz">
        <createTable tableName="gene_generatorsignalcheckinfo" remarks="gene checkSignalInfo">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GeneId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentTemplateId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SignalName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="DynamicOrStatic" type="tinyint(1)">
                <constraints nullable="false"/>
            </column>
            <column name="TriggerValue" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="Extend2" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="gene_generatorsignalcheckinfo" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="gene_generatorsignalcheckinfo" indexName="IDX_Gene_CheckSignal_1">
            <column name="GeneId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>