<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="william wu" id="active control of door" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="tbl_activecontrolofdoor" remarks="门禁控制命令表">
            <column name="StationId" type="INT" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="HostId" type="INT" remarks="主机ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="INT" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="ControlId" type="INT" remarks="控制命令ID">
                <constraints nullable="false"/>
            </column>
            <column name="UserId" type="INT" remarks="用户名">
                <constraints nullable="false"/>
            </column>
            <column name="ParameterValues" type="TEXT" remarks="参数值">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="VARCHAR(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="LastUpdate" type="datetime" remarks="最后更新时间">
                <constraints nullable="false"/>
            </column>
            <column autoIncrement="true" name="id" type="INT" remarks="自增ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>