<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_alarm_notify_element_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmNotifyElement"
                     remarks="alarm notify element table">
            <column name="ElementId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ElementName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="ElementType" type="varchar(64)">
                <constraints nullable="false"/>
            </column>
            <column name="InputNodesCount" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OutputNodesCount" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Icon" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="Expression" type="varchar(500)">
                <constraints nullable="true"/>
            </column>
            <column name="Visible" type="tinyint(1)">
                <constraints nullable="false"/>
            </column>
            <column name="SortIndex" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmNotifyElement" columnName="ElementId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>