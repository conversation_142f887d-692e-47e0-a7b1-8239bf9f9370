<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                    xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_resourceStructure_info" author="williams"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ResourceStructure" remarks="resourceStructure info table" >
            <column name="ResourceStructureId" type="int"  remarks="资源组ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SceneId" type="int" remarks="场景ID">
                <constraints nullable="true"/>
            </column>
            <column name="StructureTypeId" type="int" remarks="资源组类型">
                <constraints nullable="false"/>
            </column>
            <column name="ResourceStructureName" type="varchar(128)" remarks="分组名">
                <constraints nullable="false"/>
            </column>
            <column name="ParentResourceStructureId" type="int" remarks="父分组Id">
                <constraints nullable="true"/>
            </column>
            <column name="Photo" type="varchar(256)" remarks="图片">
                <constraints nullable="true"/>
            </column>
            <column name="Position" type="varchar(256)" remarks="位置信息">
                <constraints nullable="true"/>
            </column>
            <column name="LevelOfPath" type="varchar(128)" remarks="连接路径">
                <constraints nullable="true"/>
            </column>
            <column name="Display" type="tinyint(1)" remarks="是否显示">
                <constraints nullable="true"/>
            </column>
            <column name="SortValue" type="int" remarks="排序Index">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendedField" type="json" remarks="扩展信息">
                <constraints nullable="true"/>
            </column>
            <column name="OriginId" type="int" remarks="源对象ID">
                <constraints nullable="true"/>
            </column>
            <column name="OriginParentId" type="int" remarks="源父对象ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ResourceStructure" columnName="ResourceStructureId" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="ResourceStructure" indexName="IDX_ResourceStructure_LevelOfPath" unique="false">
            <column name="LevelOfPath"></column>
        </createIndex>
        <createIndex tableName="ResourceStructure" indexName="IDX_ResourceStructure_Type" unique="false">
            <column name="StructureTypeId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>