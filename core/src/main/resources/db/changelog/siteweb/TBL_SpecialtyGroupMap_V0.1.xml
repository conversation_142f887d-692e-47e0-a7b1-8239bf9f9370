<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblSpecialtyGroupMap_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_SpecialtyGroupMap" remarks="tblSpecialtyGroupMap info table">
            <column name="Id" type="int" remarks="意义ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SpecialtyGroupId" type="INT" remarks="权限ID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_SpecialtyGroupId_EntryItemId"/>
            </column>
            <column name="EntryItemId" type="INT" remarks="专业字典项ID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_SpecialtyGroupId_EntryItemId"/>
            </column>
            <column name="Operation" type="VARCHAR(255)" remarks="操作名"/>
        </createTable>
        <addAutoIncrement tableName="TBL_SpecialtyGroupMap" columnName="Id" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>