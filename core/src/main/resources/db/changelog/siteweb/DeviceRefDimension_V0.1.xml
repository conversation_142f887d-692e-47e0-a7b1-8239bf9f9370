<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="DeviceRefDimension" author="liaoximing"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DeviceRefDimension" remarks="DeviceRefDimension info table">
            <column name="DeviceRefDimensionId" type="int" remarks="主键ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ObjectBindData" type="int" remarks="">
                <constraints nullable="false" />
            </column>
            <column name="type" type="varchar(128)" remarks="">
                <constraints nullable="false" />
            </column>
            <column name="DimensionDesignerId" type="int" remarks="">
                <constraints nullable="false" />
            </column>
            <column name="Relation" type="varchar(128)" remarks="">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="DeviceRefDimension" columnName="DeviceRefDimensionId" incrementBy="1"
                          columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>