<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_TBL_OperationDetail_info" author="w91251" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_OperationDetail" remarks="详细操作记录表">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserId" type="int" remarks="用户ID">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectId" type="varchar(128)" remarks="操作对象ID">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectType" type="int" remarks="操作对象类型">
                <constraints nullable="false"/>
            </column>
            <column name="PropertyName" type="varchar(128)" remarks="属性名">
                <constraints nullable="true"/>
            </column>
            <column name="OperationTime" type="datetime" remarks="操作时间">
                <constraints nullable="true"/>
            </column>
            <column name="OperationType" type="varchar(64)" remarks="操作类型">
                <constraints nullable="true"/>
            </column>
            <column name="OldValue" type="varchar(255)" remarks="更改前值">
                <constraints nullable="true"/>
            </column>
            <column name="NewValue" type="varchar(255)" remarks="更改后值">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="TBL_OperationDetail" indexName="IDX_OperationDetail_1" unique="true">
            <column name="OperationTime"></column>
            <column name="UserId"></column>
        </createIndex>
        <addAutoIncrement tableName="TBL_OperationDetail" columnName="Id" incrementBy="1" columnDataType="bigint"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>