<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_management" author="cy">
        <createTable tableName="Energy_Management"
                     remarks="basic energy management table">
            <column name="EnergyId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EnergyName" type="varchar(128)">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="Unit" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Operator" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Contact" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="EndTime" type="datetime">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="Notes" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_Management" columnName="EnergyId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>