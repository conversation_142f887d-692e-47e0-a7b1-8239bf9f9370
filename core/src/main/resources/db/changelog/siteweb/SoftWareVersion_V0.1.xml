<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_softwareversion_info" author="wiliam">
        <createTable tableName="SoftwareVersion" remarks="software version info table">
            <column name="Id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ModuleName" type="varchar(128)">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="Version" type="varchar(256)">
                <constraints nullable="false" unique="false"/>
            </column>
            <column name="UpdateTime" type="DateTime">
                <constraints nullable="false" />
            </column>
            <column name="Feature" type="varchar(256)">
                <constraints nullable="false" unique="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="SoftwareVersion" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>