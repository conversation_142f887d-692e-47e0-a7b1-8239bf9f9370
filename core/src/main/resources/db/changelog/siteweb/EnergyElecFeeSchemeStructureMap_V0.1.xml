<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_schemestructuremap" author="hxh">
        <createTable tableName="Energy_ElecFeeSchemeStructureMap"
                     remarks="elecfee scheme structure map">
            <column name="MapId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
			
            <column name="SchemeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ResourceStructureName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            
            <column name="CreateDate" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateDate" type="datetime">
                <constraints nullable="false"/>
            </column>

            <column name="CreaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="CreaterName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
			
            <column name="ExtendField1" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ElecFeeSchemeStructureMap" columnName="MapId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>