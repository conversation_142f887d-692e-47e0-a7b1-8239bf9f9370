<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_extFieldConfiguration_info" author="lzy">
        <createTable tableName="ExtFieldConfiguration" remarks="扩展字段表">
            <column name="ExtId" type="int" remarks="主键">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ExtTable" type="varchar(50)" remarks="扩展关联表">
                <constraints nullable="false"/>
            </column>
            <column name="ExtCode" type="varchar(50)" remarks="扩展编码">
                <constraints nullable="true"/>
            </column>
            <column name="ExtName" type="varchar(100)" remarks="扩展名称">
                <constraints nullable="false"/>
            </column>
            <column name="ExtDesc" type="varchar(200)" remarks="扩展描述">
                <constraints nullable="true"/>
            </column>
            <column name="ExtOrder" type="int" remarks="扩展序号">
                <constraints nullable="true"/>
            </column>
            <column name="ExtNecessary" type="tinyint" remarks="扩展字段是否必须">
                <constraints nullable="true"/>
            </column>
            <column name="ExtDataType" type="varchar(50)" remarks="扩展字段数据类型">
                <constraints nullable="true"/>
            </column>
            <column name="ExtDataSource" type="varchar(300)" remarks="扩展字段数据来源">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ExtFieldConfiguration" columnName="ExtId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>