<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_template_stdsignal_map" author="hxh">
        <createTable tableName="Aircon_TemplateStdSignalMap"
                     remarks="aircon template stdSignal map">
            <column name="EquipmentTemplateId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_Template_StdSignal_Map_Table"/>
            </column>
            <column name="TypeId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_Template_StdSignal_Map_Table"/>
            </column>
            <column name="StdSignalId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_Template_StdSignal_Map_Table"/>
            </column>
            <column name="DefaultValue" type="DECIMAL(18)">
                <constraints nullable="true"/>
            </column>
            <column name="SwSignalId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="SwSignalName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="SwSignalChanelNum" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="SwCmdToken" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="SwParam" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="SwOperator" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="SwCmpValue" type="DOUBLE">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateDate" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField2" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField3" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="Aircon_TemplateStdSignalMap" indexName="IDX_TemplateStdSignalMap_1" unique="false">
            <column name="EquipmentTemplateId"></column>
            <column name="TypeId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>