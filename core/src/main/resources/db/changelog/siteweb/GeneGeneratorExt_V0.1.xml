<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_gene_generatorext" author="hxh">
        <createTable tableName="Gene_GeneratorExt" remarks="gene generatorext">

            <column name="GeneId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RatedPower" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RatedPowerConsumption" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="UnitFuelConsumption" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="DefaultElecUnitPrice" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>