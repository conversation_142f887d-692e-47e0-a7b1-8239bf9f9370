<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_elecfeeconfigoperatelog" author="hxh">
        <createTable tableName="Energy_ElecFeeConfigOperateLog"
                     remarks="elecfee config operate log">
            <column name="LogId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Operator" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="OperatorId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateDate" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="OperationContent" type="varchar(4000)">
                <constraints nullable="false"/>
            </column>
            <column name="ChangeContent" type="MEDIUMTEXT">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ElecFeeConfigOperateLog" columnName="LogId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>