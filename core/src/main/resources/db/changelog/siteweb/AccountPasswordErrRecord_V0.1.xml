<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_accountpassworderrrecord_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AccountPasswordErrRecord"
                     remarks="basic AccountPasswordErrRecord info table">
            <column name="UserId" type="int" remarks="用户ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PasswordErrCnt" type="int" remarks="密码错误累计次数">
                <constraints nullable="true"/>
            </column>
            <column name="FreezeTime" type="datetime" remarks="账号冻结目标时间">
                <constraints nullable="true" />
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>