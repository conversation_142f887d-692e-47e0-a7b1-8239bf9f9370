<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="CameraPollGroupMap" author="liaoximing" >
        <createTable tableName="CameraPollGroupMap"
                     remarks="采集周期与摄像头映射关系信息表">
            <column name="CameraPollGroupMapId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CameraPollGroupId" type="int" remarks="摄像头采集周期id">
                <constraints nullable="true" />
            </column>
            <column name="CameraId" type="int" remarks="摄像头id">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="CameraPollGroupMap" columnName="CameraPollGroupMapId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>