<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportParameterPreset_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportParameterPreset"
                     remarks="ReportParameterPreset info table">
            <column name="ReportParameterPresetId" type="int" remarks="报表参数预设ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportId" type="int" remarks="报表ID">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaQueryParameterId" type="int" remarks="报表Schema查询参数ID">
                <constraints nullable="true"/>
            </column>
            <column name="value" type="MEDIUMTEXT" remarks="值">
                <constraints nullable="true"/>
            </column>
            <column name="display" type="tinyint" remarks="是否展示">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportParameterPreset" columnName="ReportParameterPresetId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>