<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create SystemNavigation_info" author="michael">
        <createTable tableName="SystemNavigation"
                     remarks="SystemNavigation info table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Name" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="BusinessId" type="varchar(50)">
            <constraints nullable="true"/>
           </column>
            <column name="PageId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="SortValue" type="int">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="SystemNavigation" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>