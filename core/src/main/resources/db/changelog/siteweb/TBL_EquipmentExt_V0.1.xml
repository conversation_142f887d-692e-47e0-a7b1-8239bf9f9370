<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="tbl_equipmentExt" author="liaoximing">
        <createTable tableName="tbl_equipmentExt"
                     remarks="设备扩展字段表">
            <column name="equipmentId" type="int" remarks="设备主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="driveTemplateId" type="int" remarks="引用驱动模板id">
                <constraints nullable="true"/>
            </column>
            <column name="fileId" type="bigint" remarks="文件id">
                <constraints nullable="true"/>
            </column>
            <column name="isUpload" type="tinyint" remarks="是否已经上传" defaultValue="0">
                <constraints nullable="true"/>
            </column>
            <column name="isReset" type="tinyint" remarks="是否重新生成" defaultValue="1">
                <constraints nullable="true"/>
            </column>
            <column name="fieldHash" type="int" remarks="设备模板 端口 端口设置 地址 动态地址库 采集周期的哈希值(主要用于判断客户端配置工具是否对上述字段进行过修改)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>