<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_TBL_StatusBaseDic_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_StatusBaseDic" remarks="TBL_StatusBaseDic info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseStatusId" type="int" remarks="状态信号Id">
                <constraints nullable="false"/>
            </column>
            <column name="BaseStatusName" type="varchar(128)" remarks="状态信号名">
                <constraints nullable="false"/>
            </column>
            <column name="BaseCondId" type="int" remarks="基类条件ID">
                <constraints nullable="false"/>
            </column>
            <column name="Operator" type="varchar(30)" remarks="操作符">
                <constraints nullable="false"/>
            </column>
            <column name="Value" type="int" remarks="状态值">
                <constraints nullable="true"/>
            </column>
            <column name="Meaning" type="varchar(128)" remarks="状态涵义">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(256)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_StatusBaseDic" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="TBL_StatusBaseDic" indexName="IDX_StatusBaseDic_Id" unique="true">
            <column name="BaseStatusId"></column>
            <column name="BaseCondId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>