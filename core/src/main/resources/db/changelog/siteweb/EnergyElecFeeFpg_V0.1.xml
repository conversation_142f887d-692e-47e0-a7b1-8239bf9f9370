<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_elecfeefpg" author="hxh">
        <createTable tableName="Energy_ElecFeeFpg"
                     remarks="elecfee Feng Ping Gu">
            <column name="FpgId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SchemeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EffectiveStart" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="EffectiveEnd" type="datetime">
                <constraints nullable="false"/>
            </column>
            
            <column name="CreateDate" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateDate" type="datetime">
                <constraints nullable="false"/>
            </column>

            <column name="CreaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="CreaterName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>

            <column name="FpgDescKey" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="FpgDesc" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
			
            <column name="ExtendField1" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ElecFeeFpg" columnName="FpgId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>