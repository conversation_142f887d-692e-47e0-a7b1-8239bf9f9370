<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_TBL_LogicCategoryBaseDic_info" author="w91251"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_LogicCategoryBaseDic" remarks="TBL_LogicCategoryBaseDic info table">
            <column name="BaseLogicCategoryId" type="int" remarks="逻辑分类ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseEquipmentId" type="int" remarks="设备基类ID">
                <constraints nullable="false"/>
            </column>
            <column name="BaseLogicCategoryType" type="int" remarks="告警逻辑大类">
                <constraints nullable="false"/>
            </column>
            <column name="BaseLogicCategoryName" type="varchar(128)" remarks="逻辑分类名">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(256)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>