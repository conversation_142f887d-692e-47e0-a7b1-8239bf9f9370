<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_ResourceStructureMask_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ResourceStructureMask" remarks="ResourceStructureMask info table">
            <column name="ResourceStructureId" type="INT" remarks="资源组ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="TimeGroupId" type="INT" remarks="时间组ID"/>
            <column name="Reason" type="VARCHAR(255)" remarks="屏蔽原因"/>
            <column name="StartTime" type="datetime" remarks="屏蔽开始时间"/>
            <column name="EndTime" type="datetime" remarks="屏蔽结束时间"/>
            <column name="UserId" type="INT" remarks="屏蔽人"/>
        </createTable>
    </changeSet>
</databaseChangeLog>