<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportExportParameterPreset_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportExportParameterPreset"
                     remarks="ReportExportParameterPreset info table">
            <column name="ReportExportParameterPresetId" type="int" remarks="报表导出参数预设ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportId" type="int" remarks="报表ID">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaExportParameterId" type="int" remarks="报表Schema导出参数ID">
                <constraints nullable="true"/>
            </column>
            <column name="display" type="tinyint" remarks="是否展示">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportExportParameterPreset" columnName="ReportExportParameterPresetId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>