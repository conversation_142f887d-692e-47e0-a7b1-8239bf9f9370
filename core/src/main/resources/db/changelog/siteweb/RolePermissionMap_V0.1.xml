<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_rolepermissiondmap_info" author="wiliam" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="RolePermissionMap"
                     remarks="Role, Permission map info table">
            <column name="RolePermissionMapId" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RoleId" type="int" remarks="角色ID">
                <constraints nullable="false"/>
            </column>
            <column name="PermissionCategoryId" type="int" remarks="权限分类ID">
                <constraints nullable="false" />
            </column>
            <column name="PermissionId" type="int" remarks="权限ID">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="RolePermissionMap" columnName="RolePermissionMapId" incrementBy="1"  columnDataType ="int" startWith="1000" />
        <createIndex tableName="RolePermissionMap" indexName="IDX_PermissionCategoryId" unique="false">
            <column name="PermissionCategoryId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>