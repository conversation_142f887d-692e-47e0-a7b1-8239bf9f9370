<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportDataSource_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportDataSource"
                     remarks="ReportDataSource info table">
            <column name="ReportDataSourceId" type="int" remarks="报表数据源ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportDataSourceName" type="varchar(128)" remarks="报表数据源名称">
                <constraints nullable="false"/>
            </column>
            <column name="ReportDataSourceDescription" type="varchar(255)" remarks="报表数据源备注">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportDataSource" columnName="ReportDataSourceId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>