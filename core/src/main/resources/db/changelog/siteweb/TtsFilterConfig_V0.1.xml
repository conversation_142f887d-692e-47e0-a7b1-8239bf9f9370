<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create-TtsFilterConfig-table" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TtsFilterConfig" remarks="Tts过滤条件配置表">
            <column name="TtsFilterConfigId" type="int" remarks="主键自增id">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TtsStrategyId" type="int" remarks="所属策略的主键id">
                <constraints nullable="true"/>
            </column>
            <column name="TtsConfigKey" type="varchar(255)" remarks="键名">
                <constraints nullable="true"/>
            </column>
            <column name="TtsConfigValue" type="varchar(10000)" remarks="键值">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TtsFilterConfig" columnName="TtsFilterConfigId" startWith="1" incrementBy="1" columnDataType="int"/>
    </changeSet>
</databaseChangeLog>
