<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_gene_powergenerationrecord" author="hxh">
        <createTable tableName="Gene_PowerGenerationRecord"
                     remarks="gene powergenerationrecord">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentBaseType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="EndTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="PositiveElecEnergyStart" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="PositiveElecEnergyEnd" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="PowerGeneration" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RunDuration" type="bigint">
                <constraints nullable="true"/>
            </column>
            <column name="PowerGenerationCost" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="PowerGenerationOil" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="PowerGenerationOilCost" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="StartInsertTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="EndInsertTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="StartSerialNo" type="bigint">
                <constraints nullable="true"/>
            </column>
            <column name="EndSerialNo" type="bigint">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Gene_PowerGenerationRecord" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="Gene_PowerGenerationRecord" indexName="IDX_PowerGenerationRecord_EquipmentId_1">
            <column name="EquipmentId"></column>
        </createIndex>
        <createIndex tableName="Gene_PowerGenerationRecord" indexName="IDX_PowerGenerationRecord_3Fields_1">
            <column name="EquipmentId"></column>
            <column name="EquipmentBaseType"></column>
            <column name="StartTime"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>