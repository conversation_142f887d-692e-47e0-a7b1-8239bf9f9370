<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_goCronExpression_info" author="wxc">
        <createTable tableName="GoCronExpression">
            <column name="ExpressionId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Expression" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="SimpleExpression" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="ExpressionDescription" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ExpressionDescriptionEn" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="GoCronExpression" columnName="ExpressionId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>