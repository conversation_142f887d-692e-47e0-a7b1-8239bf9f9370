<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


    <changeSet id="create_MonitorUnit_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TSL_MonitorUnit" remarks="MonitorUnit info table">
            <column name="MonitorUnitId" type="int" remarks="监控单元ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="MonitorUnitName" type="varchar(128)" remarks="监控单元名">
                <constraints nullable="false"/>
            </column>
            <column name="MonitorUnitCategory" type="int" remarks="监控单元类型">
                <constraints nullable="false"/>
            </column>
            <column name="MonitorUnitCode" type="varchar(128)" remarks="监控单元编码">
                <constraints nullable="false"/>
            </column>
            <column name="WorkStationId" type="int" remarks="监控主机ID">
                <constraints nullable="true"/>
            </column>
            <column name="StationId" type="int" remarks="局站ID">
                <constraints nullable="true"/>
            </column>
            <column name="IpAddress" type="varchar(128)" remarks="IP地址">
                <constraints nullable="true"/>
            </column>
            <column name="RunMode" type="int" remarks="运行模式">
                <constraints nullable="true"/>
            </column>
            <column name="ConfigFileCode" type="varchar(32)" remarks="配置文件MD5码">
                <constraints nullable="true"/>
            </column>
            <column name="ConfigUpdateTime" type="datetime" remarks="配置更新时间">
                <constraints nullable="true"/>
            </column>
            <column name="SampleConfigCode" type="varchar(32)" remarks="采集配置MD5">
                <constraints nullable="true"/>
            </column>
            <column name="SoftwareVersion" type="varchar(64)" remarks="软件版本">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime" remarks="开始时间">
                <constraints nullable="true"/>
            </column>
            <column name="HeartBeatTime" type="datetime" remarks="心跳时间">
                <constraints nullable="true"/>
            </column>
            <column name="ConnectState" type="int" remarks="在线状态">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="false"/>
            </column>
            <column name="IsSync" type="tinyint(1)" remarks="是否需要同步">
                <constraints nullable="false"/>
            </column>
            <column name="SyncTime" type="datetime" remarks="同步时间">
                <constraints nullable="true"/>
            </column>
            <column name="IsConfigOk" type="tinyint(1)" remarks="配置是否正确">
                <constraints nullable="false"/>
            </column>
            <column name="ConfigFileCode_Old" type="varchar(32)" remarks="配置文件MD5码">
                <constraints nullable="true"/>
            </column>
            <column name="SampleConfigCode_Old" type="varchar(32)" remarks="采集配置MD5码">
                <constraints nullable="true"/>
            </column>
            <column name="AppCongfigId" type="int" remarks="配置模板ID">
                <constraints nullable="true"/>
            </column>
            <column name="CanDistribute" type="tinyint(1)" remarks="是否可分发">
                <constraints nullable="false"/>
            </column>
            <column name="Enable" type="tinyint(1)" remarks="是否可用">
                <constraints nullable="false"/>
            </column>
            <column name="ProjectName" type="varchar(255)" remarks="工程号">
                <constraints nullable="true"/>
            </column>
            <column name="ContractNo" type="varchar(255)" remarks="合同号">
                <constraints nullable="true"/>
            </column>
            <column name="InstallTime" type="datetime" remarks="安装时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="TSL_MonitorUnit" indexName="idx_MonitorUnit_Station_Id">
            <column name="StationId"></column>
        </createIndex>
        <!--addAutoIncrement tableName="TBL_Station" columnName="StationId" incrementBy="1" columnDataType="int" startWith="1"/-->
    </changeSet>
</databaseChangeLog>