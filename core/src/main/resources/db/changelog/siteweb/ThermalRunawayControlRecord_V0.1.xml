<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_thermal_runaway_control_record_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ThermalRunawayControlRecord"
                     remarks="thermal runaway control record table">
            <column name="Id" type="int" remarks="自增Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ThermalRunawayEventId" type="int" remarks="热失控事件Id">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int" remarks="控制设备Id">
                <constraints nullable="false"/>
            </column>
            <column name="ControlId" type="int" remarks="控制命令Id">
                <constraints nullable="false"/>
            </column>
            <column name="ControlValue" type="varchar(100)" remarks="控制值">
                <constraints nullable="false"/>
            </column>
            <column name="ControlTime" type="datetime" remarks="控制时间">
                <constraints nullable="false"/>
            </column>
            <column name="ControlResult" type="varchar(50)" remarks="控制结果">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ThermalRunawayControlRecord" columnName="Id" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>