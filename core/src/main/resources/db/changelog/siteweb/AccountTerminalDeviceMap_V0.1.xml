<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="AccountTerminalDeviceMap" author="yjy"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AccountTerminalDeviceMap" remarks="AccountTerminalDeviceMap info table">
            <column name="UserId" type="int" remarks="账号id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="TerminalDeviceId" type="varchar(128)" remarks="终端设备id">
                <constraints nullable="true" />
            </column>
            <column name="UpdateTime" type="datetime" remarks="操作时间">
                <constraints nullable="true" />
            </column>
            <column name="OperatorId" type="int" remarks="操作人ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>