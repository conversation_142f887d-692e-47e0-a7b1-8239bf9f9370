<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_elecfeestepprice" author="hxh">
        <createTable tableName="Energy_ElecFeeStepPrice"
                     remarks="elecfee step price">
            <column name="StepPriceId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SchemeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="StepName" type="varchar(256)">
                <constraints nullable="false"/>
            </column>
            <column name="UpperLimit" type="int">
                <constraints nullable="false"/>
            </column>
            
            <column name="CreateDate" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateDate" type="datetime">
                <constraints nullable="false"/>
            </column>

            <column name="CreaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="CreaterName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="AsMaxStep" type="int">
                <constraints nullable="true"/>
            </column>
			
            <column name="ExtendField1" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ElecFeeStepPrice" columnName="StepPriceId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>