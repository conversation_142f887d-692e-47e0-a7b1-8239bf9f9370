<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_objectmap" author="hxh">
        <createTable tableName="Energy_ObjectMap"
                     remarks="energy objectmap">

            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DimensionTypeId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="LevelId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="ObjectIdType" type="int">
                <constraints nullable="false" />
            </column>
            <column name="ParentObjectId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="ParentObjectIdType" type="int">
                <constraints nullable="false" />
            </column>
            <column name="LevelOfPath" type="varchar(1024)">
                <constraints nullable="false" />
            </column>
            <column name="Notes" type="varchar(1000)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ObjectMap" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>