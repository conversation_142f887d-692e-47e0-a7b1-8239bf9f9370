<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

<changeSet id="create_TBL_SignalBaseDic_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_SignalBaseDic" remarks="TBL_SignalBaseDic info table">
            <column name="BaseTypeId" type="bigint" remarks="基类设备ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseTypeName" type="varchar(128)" remarks="基类信号名">
                <constraints nullable="false"/>
            </column>
            <column name="BaseEquipmentId" type="int" remarks="基类设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EnglishName" type="varchar(256)" remarks="基类信号名（英文名）">
                <constraints nullable="true"/>
            </column>
            <column name="BaseLogicCategoryId" type="int" remarks="信号逻辑分类">
                <constraints nullable="true"/>
            </column>
            <column name="StoreInterval" type="int" remarks="存储周期">
                <constraints nullable="true"/>
            </column>
            <column name="AbsValueThreshold" type="double" remarks="绝对值阈值">
                <constraints nullable="true"/>
            </column>
            <column name="PercentThreshold" type="double" remarks="百分比阈值">
                <constraints nullable="true"/>
            </column>
            <column name="StoreInterval2" type="int" remarks="放电存储周期">
                <constraints nullable="true"/>
            </column>
            <column name="AbsValueThreshold2" type="double" remarks="放电存储绝对值阈值">
                <constraints nullable="true"/>
            </column>
            <column name="PercentThreshold2" type="double" remarks="百分比存储阈值">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(256)" remarks="扩展字段1">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField2" type="varchar(256)" remarks="扩展字段2">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField3" type="varchar(256)" remarks="扩展字段3">
                <constraints nullable="true"/>
            </column>
            <column name="UnitId" type="int" remarks="单位">
                <constraints nullable="true"/>
            </column>
            <column name="BaseStatusId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="BaseHysteresis" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="BaseFreqPeriod" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="BaseFreqCount" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="BaseShowPrecision" type="varchar(30)">
                <constraints nullable="true"/>
            </column>
            <column name="BaseStatPeriod" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="CGElement" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="BaseNameExt" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="IsSystem" type="tinyint(1)" defaultValue="1" remarks="是否为系统">
                <constraints nullable="false"  />
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>