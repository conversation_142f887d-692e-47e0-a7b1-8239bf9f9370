<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_dimensiontype" author="hxh">
        <createTable tableName="Energy_DimensionType"
                     remarks="energy dimensiontype">

            <column name="DimensionTypeId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DimensionTypeName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="IsUsed" type="TINYINT" defaultValue="1">
                <constraints nullable="true" />
            </column>
            <column name="OperatorUserId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="false" />
            </column>
            <column name="Notes" type="varchar(1000)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_DimensionType" columnName="DimensionTypeId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>