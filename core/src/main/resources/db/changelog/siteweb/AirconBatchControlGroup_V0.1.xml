<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_batchcontrolgroup" author="hxh">
        <createTable tableName="Aircon_BatchControlGroup"
                     remarks="aircon batchcontrolgroup">

            <column name="GroupId" type="varchar(255)" remarks="UUID PK">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GroupName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="CreatorId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="CreateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>