<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportSchemaExportParameter_info" author="psx"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportSchemaExportParameter"
                     remarks="ReportSchemaExportParameter info table">
            <column name="ReportSchemaExportParameterId" type="int" remarks="报表Schema导出参数">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportSchemaExportParameterName" type="varchar(128)" remarks="报表Schema导出参数名称">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaExportParameterTitle" type="varchar(128)" remarks="报表Schema导出参数标题">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaId" type="int" remarks="报表SchemaId">
                <constraints nullable="true"/>
            </column>
            <column name="IsNull" type="tinyint" remarks="是否为空">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportSchemaExportParameter" columnName="ReportSchemaExportParameterId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>