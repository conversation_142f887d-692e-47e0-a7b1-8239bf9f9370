<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_powerequipmentconnection_info" author="wiliam"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="PowerEquipmentConnection"
                     remarks="PowerEquipmentConnection info table">
            <column name="Id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ParentEquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="LevelOFPath" type="varchar(255)">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="PowerEquipmentConnection" columnName="Id" incrementBy="1"  columnDataType ="bigint" startWith="1" />
    </changeSet>
</databaseChangeLog>