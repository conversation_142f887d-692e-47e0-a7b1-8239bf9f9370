<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportSchemaQueryParameter_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportSchemaQueryParameter"
                     remarks="ReportSchemaQueryParameter info table">
            <column name="ReportSchemaQueryParameterId" type="int" remarks="报表Schema查询参数ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportSchemaQueryParameterName" type="varchar(128)" remarks="报表Schema查询参数名称">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaQueryParameterTitle" type="varchar(128)" remarks="报表Schema查询参数标题">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaId" type="int" remarks="报表SchemaId">
                <constraints nullable="false"/>
            </column>
            <column name="ParameterControlId" type="int" remarks="参数控制ID（前端）">
                <constraints nullable="false"/>
            </column>
            <column name="DataSourceExpression" type="Text" remarks="数据源表达式">
                <constraints nullable="true"/>
            </column>
            <column name="DataSourceReturnTableName" type="varchar(255)" remarks="数据源返回json">
                <constraints nullable="true"/>
            </column>
            <column name="IsNull" type="tinyint" remarks="是否为空">
                <constraints nullable="false"/>
            </column>
            <column name="SortIndex" type="int" remarks="展示顺序">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportSchemaQueryParameter" columnName="ReportSchemaQueryParameterId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>