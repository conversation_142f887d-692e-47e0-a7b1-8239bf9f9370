<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_reportTimingTaskFile_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportTimingTaskFile" remarks="reportTimingTaskFile info table">
            <column name="ReportTimingTaskFileId" type="int" remarks="报表定时任务文件ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportTimingTaskManagementId" type="int" remarks="报表定时任务管理ID">
                <constraints nullable="true"/>
            </column>
            <column name="reportSchemaId" type="int" remarks="报表SchemaId">
                <constraints nullable="true"/>
            </column>
            <column name="File" type="longtext" remarks="文件内容">
                <constraints nullable="true"/>
            </column>
            <column name="FilePath" type="varchar(255)" remarks="文件路径">
                <constraints nullable="true"/>
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportTimingTaskFile" columnName="ReportTimingTaskFileId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>