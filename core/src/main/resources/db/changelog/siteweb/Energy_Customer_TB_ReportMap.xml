<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="energy_customer_tb_reportmap" author="cy">
        <createTable tableName="Energy_Customer_TB_ReportMap"
                     remarks="basic energy energy_customer_tb_reportmap table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="SheetId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="RowId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="CellId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Hour" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentId" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="SignalId" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_Customer_TB_ReportMap" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>

