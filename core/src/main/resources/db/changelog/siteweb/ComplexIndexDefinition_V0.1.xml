<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_complexIndexDefinition_info" author="psx">
        <createTable tableName="ComplexIndexDefinition" remarks="complexIndexDefinition info table">
            <column name="ComplexIndexDefinitionId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ComplexIndexDefinitionName" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="CalcCron" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="CalcType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="AfterCalc" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="SaveCron" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Expression" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Accuracy" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="StartStatus" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Icon" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="CheckExpression" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ComplexIndexDefinition" columnName="ComplexIndexDefinitionId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>