<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_capacity_base_attribute_info" author="liaoximing">
        <createTable tableName="CapacityBaseAttribute" remarks="容量基类属性信息表">

            <column name="BaseAttributeId" type="int" remarks="容量基类属性ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>

            <column name="BaseAttributeName" type="varchar(64)" remarks="容量基类属性名称">
                <constraints nullable="false"/>
            </column>

            <column name="BaseTypeId" type="int" remarks="所属容量基类ID">
                <constraints nullable="false"/>
            </column>

            <column name="AttributeName" type="varchar(64)" remarks="容量分类属性名">
                <constraints nullable="true"/>
            </column>

            <column name="LogicType" type="int" remarks="逻辑处理方式 0被动更新 1指标计算">
                <constraints nullable="false"/>
            </column>

            <column name="Unit" type="varchar(16)" defaultValue="%" remarks="容量单位">
                <constraints nullable="true"/>
            </column>

            <column name="Description" type="varchar(128)" remarks="描述">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="CapacityBaseAttribute"
                          columnName="BaseAttributeId"
                          incrementBy="1"
                          columnDataType="int"
                          startWith="2000000"/>
    </changeSet>
</databaseChangeLog>