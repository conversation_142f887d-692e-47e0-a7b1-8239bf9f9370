<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="CameraPollGroup" author="liaoximing" >
        <createTable tableName="CameraPollGroup"
                     remarks="摄像头采集周期信息表">
            <column name="CameraPollGroupId" type="int" remarks="采集周期主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CameraPollGroupName" type="varchar(255)" remarks="摄像头采集周期名称">
                <constraints nullable="true" />
            </column>
            <column name="PollInterval" type="int" remarks="采集周期">
                <constraints nullable="true" />
            </column>
            <column name="Description" type="varchar(255)" remarks="描述">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="CameraPollGroup" columnName="CameraPollGroupId" incrementBy="1"  columnDataType="int" startWith="1" />
    </changeSet>
</databaseChangeLog>