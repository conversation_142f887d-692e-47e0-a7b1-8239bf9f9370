<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tslMonitorunitconfig_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TSL_MonitorUnitConfig" remarks="tslMonitorunitconfig info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AppConfigId" type="int" remarks="配置ID">
                <constraints nullable="false"/>
            </column>
            <column name="SiteWebTimeOut" type="int" remarks="超时设置">
                <constraints nullable="false"/>
            </column>
            <column name="RetryTimes" type="int" remarks="重试次数">
                <constraints nullable="false"/>
            </column>
            <column name="HeartBeat" type="int" remarks="心跳周期">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentTimeOut" type="int" remarks="设备超时配置">
                <constraints nullable="false"/>
            </column>
            <column name="PortInterruptCount" type="int" remarks="端口中断配置">
                <constraints nullable="false"/>
            </column>
            <column name="PortInitializeInternal" type="int" remarks="端口初始时间">
                <constraints nullable="false"/>
            </column>
            <column name="MaxPortInitializeTimes" type="int" remarks="最大重试次数">
                <constraints nullable="false"/>
            </column>
            <column name="PortQueryTimeOut" type="int" remarks="端口轮询时间">
                <constraints nullable="false"/>
            </column>
            <column name="DataSaveTimes" type="int" remarks="数据保存时间">
                <constraints nullable="false"/>
            </column>
            <column name="HistorySignalSavedTimes" type="int" remarks="历史数据保存时间">
                <constraints nullable="false"/>
            </column>
            <column name="HistoryBatterySavedTimes" type="int" remarks="电池数据保存时间">
                <constraints nullable="false"/>
            </column>
            <column name="HistoryEventSavedTimes" type="int" remarks="历史告警保存次数">
                <constraints nullable="false"/>
            </column>
            <column name="CardRecordSavedCount" type="int" remarks="刷卡记录保存次数">
                <constraints nullable="false"/>
            </column>
            <column name="ControlLog" type="tinyint(1)" remarks="是否记录控制日志">
                <constraints nullable="false"/>
            </column>
            <column name="IpAddressDS" type="varchar(128)" remarks="DS IP地址">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TSL_MonitorUnitConfig" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>

</databaseChangeLog>