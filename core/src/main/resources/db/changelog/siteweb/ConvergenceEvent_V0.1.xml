<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ConvergenceEvent_info" author="wiliam"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ConvergenceEvent"
                     remarks="ConvergenceEvent info table">
            <column name="Id" type="bigint">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EventName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="ConvergenceType" type="int">
                <constraints nullable="false" />
            </column>
            <column name="ConvergenceRuleId" type="int">
                <constraints nullable="false" />
            </column>
            <column name="BirthTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="ConvergenceCount" type="int">
                <constraints nullable="false" />
            </column>
            <column name="Status" type="int">
                <constraints nullable="true" />
            </column>
            <column name="ConfirmTime" type="datetime">
                <constraints nullable="true" />
            </column>
            <column name="PossibleCauses" type="varchar(1024)">
                <constraints nullable="true" />
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="EventId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="EventConditionId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="ClearTime" type="datetime">
                <constraints nullable="true" />
            </column>
            <column name="ConfirmerId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="ConfirmerName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ConvergenceEvent" columnName="Id" incrementBy="1"  columnDataType ="bigint" startWith="1" />
    </changeSet>
</databaseChangeLog>