<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="capacity_attribute_quartz_record_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="CapacityAttributeQuartzRecord" remarks="容量定时记录表">
            <column name="RecordId" type="int" remarks="记录主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseAttributeId" type="int" remarks="容量基类id">
                <constraints nullable="false"/>
            </column>
            <column name="AttributeName" type="varchar(64)" remarks="容量名称">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectId" type="int" remarks="对象id">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectTypeId" type="int" remarks="对象类型id">
                <constraints nullable="false"/>
            </column>
            <column name="RatedCapacity" type="double"  defaultValue="100" remarks="额定容量">
                <constraints nullable="false"/>
            </column>
            <column name="UsedCapacity" type="double" defaultValue="0" remarks="已使用容量">
                <constraints nullable="false"/>
            </column>
            <column name="FreeCapacity" type="double"  defaultValue="0" remarks="空闲剩余容量">
                <constraints nullable="false"/>
            </column>
            <column name="Percent" type="double"  defaultValue="0" remarks="容量百分比(已使用/空闲剩余容量)">
                <constraints nullable="false"/>
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="CapacityAttributeQuartzRecord" columnName="recordId" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="CapacityAttributeQuartzRecord" indexName="idx_createTime">
            <column name="CreateTime"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>