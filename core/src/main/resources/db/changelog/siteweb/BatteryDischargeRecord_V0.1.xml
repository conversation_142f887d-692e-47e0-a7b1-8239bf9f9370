<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="createBatterydischargerecord_item" author="habits" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="BatteryDischargeRecord"
                     remarks="BatteryDischargeRecord table">
            <column name="BatteryDischargeRecordId" type="int" remarks="电池放电记录Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BatteryStringId" type="int" remarks="电池配置Id">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentId" type="int" remarks="设备ID">
                <constraints nullable="true"/>
            </column>
            <column name="StartDischargeTime" type="datetime" remarks="放电开始时间">
                <constraints nullable="true" />
            </column>
            <column name="EndDischargeTime" type="datetime" remarks="放电结束时间">
                <constraints nullable="true" />
            </column>
            <column name="SignalId" type="int" remarks="放电触发的信号ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="BatteryDischargeRecord" columnName="BatteryDischargeRecordId" incrementBy="1"
                          columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>