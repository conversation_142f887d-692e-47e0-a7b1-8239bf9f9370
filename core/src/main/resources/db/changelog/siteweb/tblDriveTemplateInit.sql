delete from tbl_drivetemplate where id in (1, 2);
delete from tbl_drivestructuretemplate where driveTemplateId in (1,2);

    -- 初始化默认驱动模板 --
INSERT INTO tbl_drivetemplate (id, driveTemplateName, driverTemplateDescribe, isDefaultTemplate, driveTemplateType)VALUES (1, 'SNMP', 'SNMP默认结构模板', 1, 1);
INSERT INTO tbl_drivetemplate (id, driveTemplateName, driverTemplateDescribe, isDefaultTemplate, driveTemplateType)VALUES (2, 'BACNet', 'BACNet默认结构模板', 1, 2);

    -- 初始化默认驱动结构 --
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (1, '{workStationId}{monitorUnitName}', 0, NULL, 1, 0, 0, 1, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (2, 'SnmpManager', 1, NULL, 1, 0, 0, 1, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (3, 'Equipment{address}', 2, NULL, 1, 0, 0, 1, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (4, 'OidFile', 3, NULL, 1, 0, 0, 1, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (5, 'SO', 1, NULL, 1, 0, 0, 1, 0, 0);

INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (6, '{workStationId}{monitorUnitName}', 0, NULL, 1, 0, 0, 2, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (7, 'BAManager', 6, NULL, 1, 0, 0, 2, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (8, 'SO', 6, NULL, 1, 0, 0, 2, 0, 0);
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming)VALUES (9, 'BACNet_{equipmentId}.ini', 7, NULL, 0, 1, 1, 2, 1, 1);


-- 初始化snmp的ac.oid文件
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming) VALUES(10, 'ac.oid', 4, 1000, 0, 1, 0, 1, 1, 1);
insert into diskfile (FileId, FilePath, FileName, Status, CreateTime) values(1000, 'batchtool', '(template)ac.oid', 1, now());

-- 初始化snmp的SnmpManager_X.cfg文件
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming) VALUES(11, 'SnmpManager_{address}.cfg', 3, 1001, 0, 1, 1, 1, 1, 0);
insert into diskfile (FileId, FilePath, FileName, Status, CreateTime) values(1001, 'batchtool', '(template)SnmpManager_{address}.cfg', 1, now());

-- 初始化snmp的ac.control文件
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming) VALUES(12, 'ac.control', 4, 1002, 0, 1, 0, 1, 1, 0);
insert into diskfile (FileId, FilePath, FileName, Status, CreateTime) values(1002, 'batchtool', '(template)ac.control', 1, now());

-- 初始化snmp的snmp_{address}.so文件
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming) VALUES(13, '{dllPath}', 5, 1003, 0, 1, 0, 1, 1, 0);
insert into diskfile (FileId, FilePath, FileName, Status, CreateTime) values(1003, 'batchtool', '(template)SNMP{address}.so', 1, now());

-- 初始化bacnet的BANet{address}.so
INSERT INTO tbl_drivestructuretemplate(id, filePath, pid, fileId, isDisk, isUpload, isFill, driveTemplateId, isLeaf, uploadTiming) VALUES(14, '{dllPath}', 8, 1004, 0, 1, 0, 2, 1, 0);
insert into diskfile (FileId, FilePath, FileName, Status, CreateTime) values(1004, 'batchtool', '(template)BANet{address}.so', 1, now());
