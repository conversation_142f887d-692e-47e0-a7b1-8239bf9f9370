<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblUserRoleMap_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_UserRoleMap" remarks="tblUserRoleMap info table">
            <column name="Id" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserId" type="int" remarks="用户ID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_UserId_RoleId"/>
            </column>
            <column name="RoleId" type="int" remarks="角色ID">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_UserId_RoleId"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_UserRoleMap" columnName="Id" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>