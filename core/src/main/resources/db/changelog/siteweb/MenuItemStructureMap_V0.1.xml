<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_MenuItemStructureMap_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="MenuItemStructureMap"
                     remarks="MenuItemStructureMap info table">
            <column name="MenuItemStructureMapId" type="int" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="MenuProfileId" type="int" remarks="菜单方案ID">
                <constraints nullable="false"/>
            </column>
            <column name="MenuStructureId" type="int" remarks="菜单目录ID">
                <constraints nullable="false"/>
            </column>
            <column name="MenuItemId" type="int" remarks="菜单ID">
                <constraints nullable="false"/>
            </column>
            <column name="SortIndex" type="int" remarks="排序索引">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="MenuItemStructureMap" columnName="MenuItemStructureMapId" incrementBy="1" columnDataType="int" startWith="1000"/>
    </changeSet>
</databaseChangeLog>