<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_graphicpagetemplate_info" author="wiliam"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="GraphicPageTemplate" remarks="graphicpagetemplate info table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Type" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="GroupId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="PageCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="BaseEquipmentId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Internal" type="tinyint">
                <constraints nullable="true"/>
            </column>
            <column name="Name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="AppendName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="Data" type="Text">
                <constraints nullable="true"/>
            </column>
            <column name="Style" type="Text">
                <constraints nullable="true"/>
            </column>
            <column name="Children" type="longText">
                <constraints nullable="true"/>
            </column>
            <column name="TemplateCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="GraphicPageTemplate" columnName="Id" incrementBy="1"  columnDataType ="int" startWith="1" />
        <createIndex tableName="GraphicPageTemplate" indexName="IDX_GraphicPageTemplate_1" unique="false">
            <column name="PageCategory"></column>
            <column name="BaseEquipmentId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>