<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet author="William Wu" id="Active Control INFO" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_ActiveControl" remarks="活动告警表">
            <column name="StationId" type="INT" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="StationName" type="VARCHAR(255)" remarks="局站名">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="INT" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentName" type="VARCHAR(128)" remarks="设备名">
                <constraints nullable="false"/>
            </column>
            <column name="ControlId" type="INT" remarks="控制命令ID">
                <constraints nullable="false"/>
            </column>
            <column name="ControlName" type="VARCHAR(128)" remarks="控制命令名">
                <constraints nullable="false"/>
            </column>
            <column autoIncrement="true" name="SerialNo" type="INT" remarks="流水号（自增）">
                <constraints nullable="false" primaryKey="true" unique="true"/>/>
            </column>
            <column name="ControlSeverity" type="INT" remarks="控制等级">
                <constraints nullable="false"/>
            </column>
            <column name="CmdToken" type="text" remarks="控制命令串">
                <constraints nullable="false"/>
            </column>
            <column name="ControlPhase" type="INT" remarks="控制阶段">
                <constraints nullable="false"/>
            </column>
            <column name="StartTime" type="datetime" remarks="开始时间">
                <constraints nullable="false"/>
            </column>
            <column name="EndTime" type="datetime" remarks="结束时间">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmTime" type="datetime" remarks="确认时间">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmerId" type="INT" remarks="确认人ID">
                <constraints nullable="true"/>
            </column>
            <column name="ConfirmerName" type="VARCHAR(255)" remarks="确认人名">
                <constraints nullable="true"/>
            </column>
            <column name="ControlResultType" type="INT" remarks="控制结果ID">
                <constraints nullable="true"/>
            </column>
            <column name="ControlResult" type="VARCHAR(255)" remarks="控制结果">
                <constraints nullable="true"/>
            </column>
            <column name="ControlExecuterId" type="INT" remarks="执行人Id">
                <constraints nullable="true"/>
            </column>
            <column name="ControlExecuterIdName" type="VARCHAR(255)" remarks="执行人名">
                <constraints nullable="true"/>
            </column>
            <column name="ControlType" type="INT" remarks="控制类型">
                <constraints nullable="true"/>
            </column>
            <column name="ActionId" type="INT" remarks="联动ID">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="VARCHAR(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="Retry" type="INT" remarks="重试次数">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeId" type="DECIMAL(12)" remarks="控制基类ID">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeName" type="VARCHAR(128)" remarks="控制基类名">
                <constraints nullable="true"/>
            </column>
            <column name="ParameterValues" type="TEXT" remarks="控制参数">
                <constraints nullable="false"/>
            </column>
            <column name="BaseCondId" type="DECIMAL(12)" remarks="基类参数Id">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>