<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

<changeSet id="create_tblEquipmenttemplate_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_EquipmentTemplate" remarks="Equipmenttemplate info table">
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentTemplateName" type="varchar(128)" remarks="设备模板名">
                <constraints nullable="false"/>
            </column>
            <column name="ParentTemplateId" type="int" remarks="父模板Id">
                <constraints nullable="false"/>
            </column>
            <column name="Memo" type="varchar(255)" remarks="备注信息">
                <constraints nullable="false"/>
            </column>
            <column name="ProtocolCode" type="varchar(32)" remarks="协议编码MD5">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentCategory" type="int" remarks="设备类型ID（内部）">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentType" type="int" remarks="设备分类（自诊断，虚拟设备等)">
                <constraints nullable="false"/>
            </column>
            <column name="Property" type="varchar(255)" remarks="设备类型">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentStyle" type="varchar(128)" remarks="设备型号">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(255)" remarks="设备单位">
                <constraints nullable="true"/>
            </column>
            <column name="Vendor" type="varchar(255)" remarks="设备厂商">
                <constraints nullable="true"/>
            </column>
            <column name="Photo" type="varchar(255)" remarks="图片">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentBaseType" type="int" remarks="基类设备ID">
                <constraints nullable="true"/>
            </column>
            <column name="StationCategory" type="int" remarks="局站类型(有些模板只能用在机房或者节点)">
                <constraints nullable="true"/>
            </column>
            <column name="SecondaryCategory" type="int" remarks="第二设备类型">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <!--addAutoIncrement tableName="TBL_EquipmentTemplate" columnName="EquipmentTemplateId" incrementBy="1" columnDataType="int" startWith="1"/-->
    </changeSet>
</databaseChangeLog>