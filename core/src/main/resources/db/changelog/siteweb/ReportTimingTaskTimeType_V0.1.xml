<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_reportTimingTaskTimeType_info" author="wxc">
        <createTable tableName="ReportTimingTaskTimeType" remarks="reportTimingTaskTimeType info table">
            <column name="ReportTimingTaskTimeTypeId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="TimeTypeName" type="varchar(45)">
                <constraints nullable="true"/>
            </column>
            <column name="TimeTypeValue" type="varchar(45)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportTimingTaskTimeType" columnName="ReportTimingTaskTimeTypeId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>