<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

     <changeSet id="create_tblEventcondition_info" author="williams_wu" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_EventCondition" remarks="tblEventcondition info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EventConditionId" type="int" remarks="告警条件ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="int" remarks="告警ID">
                <constraints nullable="false"/>
            </column>
            <column name="StartOperation" type="varchar(4)" remarks="开始运算符">
                <constraints nullable="false"/>
            </column>
            <column name="StartCompareValue" type="double" remarks="开始比较值">
                <constraints nullable="false"/>
            </column>
            <column name="StartDelay" type="int" remarks="开始延时">
                <constraints nullable="false"/>
            </column>
            <column name="EndOperation" type="varchar(4)" remarks="结束运算符">
                <constraints nullable="true"/>
            </column>
            <column name="EndCompareValue" type="double" remarks="结束比较值">
                <constraints nullable="true"/>
            </column>
            <column name="EndDelay" type="int" remarks="结束延时">
                <constraints nullable="true"/>
            </column>
            <column name="Frequency" type="int" remarks="频次告警次数">
                <constraints nullable="true"/>
            </column>
            <column name="FrequencyThreshold" type="int" remarks="频次告警时间">
                <constraints nullable="true"/>
            </column>
            <column name="Meanings" type="varchar(255)" remarks="告警涵义">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentState" type="int" remarks="告警设备状态">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeId" type="bigint" remarks="标准化ID">
                <constraints nullable="true"/>
            </column>
            <column name="EventSeverity" type="int" remarks="告警等级ID">
                <constraints nullable="false"/>
            </column>
            <column name="StandardName" type="int" remarks="标准化ID（旧版，已经不用）">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_EventCondition" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
         <createIndex tableName="TBL_EventCondition" indexName="IDX_TBL_EventCondition_Id" unique="true">
             <column name="EquipmentTemplateId"></column>
             <column name="EventId"></column>
             <column name="EventConditionId"></column>
         </createIndex>
    </changeSet>


</databaseChangeLog>