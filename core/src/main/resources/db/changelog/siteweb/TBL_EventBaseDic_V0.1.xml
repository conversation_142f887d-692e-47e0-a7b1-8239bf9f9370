<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

     <changeSet id="create_TBL_EventBaseDic_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_EventBaseDic" remarks="TBL_EventBaseDic info table">
            <column name="BaseTypeId" type="bigint" remarks="告警基类ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseTypeName" type="varchar(128)" remarks="告警基类名">
                <constraints nullable="false"/>
            </column>
            <column name="BaseEquipmentId" type="int" remarks="基类设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EnglishName" type="varchar(256)" remarks="告警基类名（英文）">
                <constraints nullable="true"/>
            </column>
            <column name="EventSeverityId" type="int" remarks="告警等级ID">
                <constraints nullable="false"/>
            </column>
            <column name="ComparedValue" type="double" remarks="告警开始值">
                <constraints nullable="true"/>
            </column>
            <column name="BaseLogicCategoryId" type="int" remarks="告警逻辑分类">
                <constraints nullable="true"/>
            </column>
            <column name="StartDelay" type="int" remarks="告警开始延时">
                <constraints nullable="true"/>
            </column>
            <column name="EndDelay" type="int" remarks="告警结束延时">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(256)" remarks="扩展信息1">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField2" type="varchar(256)" remarks="扩展信息2">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField3" type="varchar(256)" remarks="扩展信息3">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField4" type="varchar(256)" remarks="扩展信息4">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField5" type="varchar(256)" remarks="扩展信息5">
                <constraints nullable="true"/>
            </column>
            <column name="BaseNameExt" type="varchar(128)" remarks="扩展信息（用在多模块的场合）">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(256)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="IsSystem" type="tinyint(1)" defaultValue="1" remarks="是否为系统">
                <constraints nullable="false"  />
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>