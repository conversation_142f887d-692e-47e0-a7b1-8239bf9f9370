<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_ratingdatahistory" author="lqp">
        <createTable tableName="energy_ratingdatahistory"
                     remarks="">

            <column name="RatingDataId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RatingConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SampleTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="IntervalSecond" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="WorkingCondition" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="OutDryTemp" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="OutWetTemp" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="InDryTemp" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="InWetTemp" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="RunningLoad" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="ITPower" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TotalPower" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="Times" type="int" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="DeleteDate" type="datetime">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="energy_ratingdatahistory" columnName="RatingDataId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>