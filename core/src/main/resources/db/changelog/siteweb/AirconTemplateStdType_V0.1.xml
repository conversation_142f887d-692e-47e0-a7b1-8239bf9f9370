<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_template_stdType" author="hxh">
        <createTable tableName="Aircon_TemplateStdType"
                     remarks="aircon template stdtype">
            <column name="EquipmentTemplateId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="TypeId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="TypeName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateDate" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>