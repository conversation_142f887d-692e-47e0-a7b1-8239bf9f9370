<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_businessdefinitionmap" author="hxh">
        <createTable tableName="Energy_BusinessDefinitionMap"
                     remarks="">
            <column name="MapId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BusinessTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ComplexIndexDefinitionId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ComplexIndexDefinitionTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField1" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_BusinessDefinitionMap" columnName="MapId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>