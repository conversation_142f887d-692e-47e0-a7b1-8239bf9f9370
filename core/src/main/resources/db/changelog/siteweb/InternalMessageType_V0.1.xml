<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_internalmessagetype_info" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="InternalMessageType" remarks="basic InternalMessageType info table">
            <column name="InternalMessageTypeId" type="int" remarks="ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TypeName" type="varchar(32)" remarks="消息类型">
                <constraints nullable="false"/>
            </column>
            <column name="EnableTts" type="int" remarks="是否启用TTS播报">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="InternalMessageType" columnName="InternalMessageTypeId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>