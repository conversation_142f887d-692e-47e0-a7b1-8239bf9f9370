<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_PreAlarmCategory_info" author="xueyi">
        <createTable tableName="PreAlarmCategory" remarks="PreAlarmCategory info table">
            <column name="CategoryId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="CategoryName" type="varchar(64)">
                <constraints nullable="false" />
            </column>
            <column name="ParentCategoryId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(128)">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="PreAlarmCategory" columnName="CategoryId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>