<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">


<changeSet id="create_TBL_EquipmentBaseType_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_EquipmentBaseType" remarks="TBL_EquipmentBaseType info table">
            <column name="BaseEquipmentId" type="int" remarks="基类设备ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseEquipmentName" type="varchar(128)" remarks="基类设备名">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentTypeId" type="int" remarks="设备大类ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentSubTypeId" type="int" remarks="设备子类ID">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(256)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <!--<addAutoIncrement tableName="TBL_EquipmentBaseType" columnName="BaseEquipmentId" incrementBy="1" -->
        <!--columnDataType="int" startWith="1"/>-->
    </changeSet>
</databaseChangeLog>