<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tblStationMask_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_StationMask" remarks="tblStationMask info table">
            <column name="StationId" type="INT" remarks="局站ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="TimeGroupId" type="INT" remarks="屏蔽时间段ID"/>
            <column name="Reason" type="VARCHAR(255)" remarks="屏蔽原因"/>
            <column name="StartTime" type="datetime" remarks="屏蔽开始时间"/>
            <column name="EndTime" type="datetime" remarks="屏蔽结束时间"/>
            <column name="UserId" type="INT" remarks="用户ID"/>
        </createTable>
    </changeSet>
</databaseChangeLog>