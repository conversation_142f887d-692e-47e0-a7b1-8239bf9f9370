<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="ComputerRackSignalMap" author="shj" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ComputerRackSignalMap" remarks="ComputerRackSignalMap info table">
            <column name="computerRackSignalMapId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="computerRackId" type="int" remarks="机架id">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="openExpression" type="varchar(256)">
                <constraints nullable="true" />
            </column>
            <column name="powerExpression" type="varchar(256)">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="ComputerRackSignalMap" columnName="computerRackSignalMapId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>