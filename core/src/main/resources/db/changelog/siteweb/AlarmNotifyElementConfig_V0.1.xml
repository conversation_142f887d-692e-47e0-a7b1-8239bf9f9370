<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_alarm_notify_element_config_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmNotifyElementConfig"
                     remarks="alarm notify element config table">
            <column name="AlarmNotifyElementConfigId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AlarmNotifyConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ElementId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Expression" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(500)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField2" type="varchar(500)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField3" type="varchar(500)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmNotifyElementConfig" columnName="AlarmNotifyElementConfigId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>