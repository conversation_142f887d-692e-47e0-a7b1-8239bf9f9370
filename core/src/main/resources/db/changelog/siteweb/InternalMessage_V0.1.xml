<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_internalmessage_info" author="liao.ximing.ming" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="InternalMessage" remarks="basic InternalMessage  info table">
            <column name="InternalMessageId" type="int" remarks="ID">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="Body" type="varchar(512)" remarks="消息内容">
                <constraints nullable="false" />
            </column>
            <column name="MessageType" type="int" remarks="消息类型">
                <constraints nullable="false"/>
            </column>
            <column name="CreateTime" type="int" remarks="创建时间">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="InternalMessage" columnName="InternalMessageId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>