<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="energy_customer_tb_reportrecord" author="cy">
        <createTable tableName="Energy_Customer_TB_ReportRecord"
                     remarks="basic energy energy_customer_tb_reportrecord table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="CreateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="ReportName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="FilePath" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="Notes" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_Customer_TB_ReportRecord" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>

