<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="gene_generatorhistorysignal" author="lz">
        <createTable tableName="gene_generatorhistorysignal" remarks="gene historySignalValue">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GeneId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="GeneIdAndSignalId" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="SignalValue" type="Double">
                <constraints nullable="true"/>
            </column>
            <column name="InsertTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="SignalValid" type="tinyint(1)">
                <constraints nullable="false"/>
            </column>
            <column name="PowerSerialId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="MakeAlarm" type="tinyint">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="gene_generatorhistorysignal" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="gene_generatorhistorysignal" indexName="IDX_Gene_HistorySignal_1">
            <column name="GeneIdAndSignalId"></column>
        </createIndex>
        <createIndex tableName="gene_generatorhistorysignal" indexName="IDX_Gene_HistorySignal_2">
            <column name="GeneId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>