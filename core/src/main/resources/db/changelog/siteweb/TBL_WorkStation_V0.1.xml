<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_WorkStation_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_WorkStation" remarks="WorkStation info table">
            <column name="WorkStationId" type="int" remarks="监控主机ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="WorkStationName" type="varchar(128)" remarks="监控主机名">
                <constraints nullable="false"/>
            </column>
            <column name="WorkStationType" type="int" remarks="监控主机类型">
                <constraints nullable="false"/>
            </column>
            <column name="IPAddress" type="varchar(128)" remarks="IP地址">
                <constraints nullable="false"/>
            </column>
            <column name="ParentId" type="int" remarks="父主机ID">
                <constraints nullable="true"/>
            </column>
            <column name="ConnectState" type="int" remarks="在线状态">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="false"/>
            </column>
            <column defaultValue="1" name="IsUsed" type="tinyint(1)" remarks="是否投用">
                <constraints nullable="false"/>
            </column>
            <column name="CPU" type="double" remarks="CPU百分比">
                <constraints nullable="true"/>
            </column>
            <column name="Memory" type="double" remarks="Memory占用百分比">
                <constraints nullable="true"/>
            </column>
            <column name="ThreadCount" type="int" remarks="线程数量">
                <constraints nullable="true"/>
            </column>
            <column name="DiskFreeSpace" type="double" remarks="磁盘剩余空间">
                <constraints nullable="true"/>
            </column>
            <column name="DBFreeSpace" type="double" remarks="数据库剩余控件">
                <constraints nullable="true"/>
            </column>
            <column name="LastConnTime" type="datetime" remarks="上次连接时间">
                <constraints nullable="true"/>
            </column>
            <column name="Comment" type="varchar(500)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>