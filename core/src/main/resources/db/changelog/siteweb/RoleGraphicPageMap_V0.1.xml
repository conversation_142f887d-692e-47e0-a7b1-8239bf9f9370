<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_RoleGraphicPageMap_info" author="yangjingyi" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="RoleGraphicPageMap" remarks="RoleGraphicPageMap info table">
            <column name="RoleId" type="int" remarks="角色id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GraphicPageId" type="int" remarks="组态id">
                <constraints nullable="false"/>
            </column>
            <column name="Config" type="text" remarks="配置">
                <constraints nullable="false"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>