<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_menu_permission_group_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="MenuPermissionGroup" remarks="MenuPermissionGroup info table">
            <column name="MenuPermissionGroupId" type="INT" remarks="菜单权限组表主键id">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="MenuPermissionGroupName" type="VARCHAR(128)" remarks="菜单权限组名称">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="VARCHAR(255)" remarks="描述信息"/>
        </createTable>
        <addAutoIncrement tableName="MenuPermissionGroup" columnName="MenuPermissionGroupId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>