<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="AlarmVideoLinkMap" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmVideoLinkMap" remarks="AlarmVideoLinkMap info table">
            <column name="AlarmVideoLinkMapId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AlarmVideoLinkId" type="int" remarks="告警视频联动id">
                <constraints nullable="false" />
            </column>
            <column name="EquipmentId" type="int" remarks="设备id">
                <constraints nullable="false" />
            </column>
            <column name="EventId" type="int" remarks="告警id">
                <constraints nullable="false" />
            </column>
            <column name="EventConditionId" type="int" remarks="告警条件id">
                <constraints nullable="false" />
            </column>
            <column name="CameraId" type="bigint" remarks="摄像头id">
                <constraints nullable="false" />
            </column>
            <column name="CameraIds" type="varchar(255)" remarks="摄像头ids，多个逗号隔开">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmVideoLinkMap" columnName="AlarmVideoLinkMapId" incrementBy="1"  columnDataType ="int" startWith="1" />
        <createIndex tableName="AlarmVideoLinkMap" indexName="IDX_EquipmentId_EventId_EventConditionId">
            <column name="EquipmentId"/>
            <column name="EventId"/>
            <column name="EventConditionId"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>