<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_alarm_notify_segment_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmNotifySegment"
                     remarks="alarm notify segment table">
            <column name="SegmentId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="InputElementConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="InputNodeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OutputElementConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OutputNodeId" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmNotifySegment" columnName="SegmentId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>