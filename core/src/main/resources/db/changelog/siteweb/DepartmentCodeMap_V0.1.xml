<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="DepartmentCodeMap" author="yjy" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DepartmentCodeMap" remarks="DepartmentCodeMap info table">
            <column name="DepartmentId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ParentDepartmentId" type="int" remarks="父部门id">
                <constraints nullable="true" />
            </column>
            <column name="Code" type="varchar(256)" remarks="第三方部门唯一标识">
                <constraints nullable="false" />
            </column>
            <column name="ParentCode" type="varchar(256)" remarks="第三方父部门唯一标识">
                <constraints nullable="true" />
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>