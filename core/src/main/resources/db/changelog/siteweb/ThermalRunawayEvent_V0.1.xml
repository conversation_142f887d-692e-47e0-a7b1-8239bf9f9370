<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_thermal_runaway_event_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ThermalRunawayEvent"
                     remarks="thermal runaway event table">
            <column name="ThermalRunawayEventId" type="int" remarks="热失控事件Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StartTime" type="datetime" remarks="开始时间">
                <constraints nullable="false"/>
            </column>
            <column name="EndTime" type="datetime" remarks="结束时间">
                <constraints nullable="true"/>
            </column>
            <column name="BatteryPackId" type="int" remarks="电池单体Id">
                <constraints nullable="false"/>
            </column>
            <column name="TriggerTemperature" type="DOUBLE" remarks="触发热失控时的单体温度">
                <constraints nullable="false"/>
            </column>
            <column name="Prediction" type="varchar(128)" remarks="预测结果">
                <constraints nullable="true"/>
            </column>
            <column name="PhaseId" type="int" remarks="所处阶段ID">
                <constraints nullable="true"/>
            </column>
            <column name="Phase" type="varchar(128)" remarks="所处阶段">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ThermalRunawayEvent" columnName="ThermalRunawayEventId" incrementBy="1"
                          columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>