<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_gene_maintenancerecord" author="hxh">
        <createTable tableName="Gene_MaintenanceRecord" remarks="gene maintenancerecord">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GeneId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="GeneName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="MaintenanceTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="Position" type="varchar(1024)">
                <constraints nullable="true"/>
            </column>
            <column name="MaintenanceItem" type="varchar(1024)">
                <constraints nullable="true"/>
            </column>
            <column name="Maintainer" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Confirmor" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="Remark" type="varchar(1024)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Gene_MaintenanceRecord" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="Gene_MaintenanceRecord" indexName="IDX_Gene_MaintenanceRecord_1">
            <column name="GeneId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>