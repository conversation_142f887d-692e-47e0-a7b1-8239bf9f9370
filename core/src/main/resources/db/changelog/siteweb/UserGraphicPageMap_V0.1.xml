<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_UserGraphicPageMap_info" author="liaoximing" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="UserGraphicPageMap" remarks="UserGraphicPageMap info table">
            <column name="UserGraphicPageMapId" type="int" remarks="用户组态映射id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UserId" type="int" remarks="用户id">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicPageId" type="int" remarks="组态id">
                <constraints nullable="false"/>
            </column>
            <column name="Config" type="text" remarks="配置">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="UserGraphicPageMap" columnName="UserGraphicPageMapId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>