<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_GraphicTemplate_info" author="liaoximing">
        <createTable tableName="GraphicTemplate" remarks="GraphicTemplate info table">
            <column name="GraphicTemplateId" type="int" remarks="组态模板自增id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="GraphicTemplateName" type="varchar(128)" remarks="模板名称">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateTag" type="varchar(128)" remarks="模板标签">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateCompType" type="int" remarks="组件类型">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateTypeId" type="int" remarks="组件id">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateType" type="varchar(128)" remarks="组件英文名称">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateTypeName" type="varchar(128)" remarks="组件中文名称">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateCover" type="varchar(128)" remarks="模板缩略图">
                <constraints nullable="false"/>
            </column>
            <column name="GraphicTemplateConfig" type="text" remarks="模板配置">
                <constraints nullable="false"/>
            </column>
            <column name="CreateTime" type="datetime" remarks="创建时间">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="GraphicTemplate" columnName="GraphicTemplateId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>