<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_ratingdata" author="lqp">
        <createTable tableName="energy_ratingdata" remarks="">
            <column name="RatingDataId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RatingConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SampleTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="IntervalSecond" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="WorkingCondition" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="OutDryTemp" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="OutWetTemp" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="InDryTemp" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="InWetTemp" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RunningLoad" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="ITPower" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="TotalPower" type="double">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <createIndex tableName="energy_ratingdata" indexName="energy_ratingdata_idx1" unique="false">
            <column name="RatingConfigId"/>
            <column name="SampleTime"/>
            <column name="WorkingCondition"/>
        </createIndex>
        <addAutoIncrement tableName="energy_ratingdata" columnName="RatingDataId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>