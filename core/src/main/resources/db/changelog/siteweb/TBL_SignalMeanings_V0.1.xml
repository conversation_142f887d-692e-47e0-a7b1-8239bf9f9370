<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">
    <changeSet id="create_tblSignalmeanings_info" author="williams_wu" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_SignalMeanings" remarks="tblSignalmeanings info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="false"/>
            </column>
            <column name="SignalId" type="int" remarks="信号Id">
                <constraints nullable="false"/>
            </column>
            <column name="StateValue" type="smallint" remarks="信号值">
                <constraints nullable="false"/>
            </column>
            <column name="Meanings" type="varchar(255)" remarks="信号涵义">
                <constraints nullable="true"/>
            </column>
            <column name="BaseCondId" type="bigint" remarks="基类ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_SignalMeanings" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    <createIndex tableName="TBL_SignalMeanings" indexName="IDX_SignalMeanings_Id" unique="false">
        <column name="EquipmentTemplateId"></column>
        <column name="SignalId"></column>
    </createIndex>
    </changeSet>
</databaseChangeLog>