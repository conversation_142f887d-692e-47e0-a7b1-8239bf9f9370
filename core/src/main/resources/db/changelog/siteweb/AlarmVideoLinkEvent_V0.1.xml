<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="AlarmVideoLinkEvent" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmVideoLinkEvent" remarks="AlarmVideoLinkMap info table">
            <column name="AlarmVideoLinkEventId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AlarmVideoLinkMapId" type="int" remarks="告警视频联动映射关系id">
                <constraints nullable="false" />
            </column>
            <column name="EventId" type="int" remarks="事件id">
                <constraints nullable="false" />
            </column>
            <column name="EventConditionId" type="int" remarks="事件条件id">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmVideoLinkEvent" columnName="AlarmVideoLinkEventId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>