<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="AlarmNotifyGatewayServiceConfig" author="yjy" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmNotifyGatewayServiceConfig" remarks="AlarmNotifyGatewayServiceConfig info table">
            <column name="AlarmNotifyGatewayServiceConfigId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AlarmNotifyConfigId" type="int" remarks="策略id">
                <constraints nullable="false" />
            </column>
            <column name="AlarmNotifyGatewayServiceId" type="int" remarks="AlarmNotifyGatewayServiceId">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmNotifyGatewayServiceConfig" columnName="AlarmNotifyGatewayServiceConfigId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>