<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_sub_scene_menu_profile_map_info" author="psx">
        <createTable tableName="SceneMenuProfileMap"
                     remarks="scene menu profile map info table">
            <column name="SceneMenuProfileMapId" type="int" remarks="主键">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SceneId" type="int" remarks="场景Id">
                <constraints nullable="false" />
            </column>
            <column name="MenuProfileId" type="int" remarks="菜单方案">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="SceneMenuProfileMap" columnName="SceneMenuProfileMapId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>