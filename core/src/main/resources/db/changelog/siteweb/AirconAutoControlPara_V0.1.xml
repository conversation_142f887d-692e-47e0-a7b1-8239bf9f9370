<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_autocontrolpara" author="hxh">
        <createTable tableName="Aircon_AutoControlPara"
                     remarks="aircon autocontrolpara">

            <column name="VirtualEquipmentId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="VirtualEquipmentName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="GroupName" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SamplerAddress" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="MonitorUnitId" type="int">
                <constraints nullable="false"/>
            </column>

            <column name="ParaEnable" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="RollingCount" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="TempComputeMode" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempCoolAll" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempCoolStart" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="WorkTemp" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempInFanStop" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempBottomLow" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempSetting" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="RunPeriod" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperationInterval" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="TempDiff" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="FanInstall" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="TempFanStart" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="OutInTempDiff" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempOutFanStop" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="EnableWarm" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="TempHot" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempHotStart" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="TempHotAll" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="LastSuccussDeployTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="LastDeployState" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="LastDeployTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Operationor" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>