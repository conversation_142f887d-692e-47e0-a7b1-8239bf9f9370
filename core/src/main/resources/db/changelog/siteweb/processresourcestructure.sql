DROP function IF EXISTS fuct_GetDevicePosition;

CREATE function `fuct_GetDevicePosition` (StructureId int)  RETURNS VARCHAR(1000)
    DETERMINISTIC
BEGIN
    DECLARE tempParentId VARCHAR(128);
    DECLARE tempParent varchar(255);
    DECLARE tempId int;
    SELECT  replace(levelofpath, '.', ','),resourcestructureId  into tempParentId, tempId from resourcestructure where resourcestructureId = StructureId;

    if (tempId = 0) then
      return tempParent;
    end if;
    -- 去除根节点的名称
    SELECT SUBSTRING(tempParentId, LOCATE(',', tempParentId) + 1) into tempParentId;
    SELECT GROUP_CONCAT(c.resourcestructureName separator'_')  into tempParent from resourcestructure c where FIND_IN_SET( resourcestructureId,  tempParentId);
    return tempParent;
END;

