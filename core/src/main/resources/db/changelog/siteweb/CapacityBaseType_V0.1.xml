<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_capacity_base_type_info" author="Liu.Yandong">
        <createTable tableName="CapacityBaseType" remarks="容量基类信息表">
            <column name="BaseTypeId" type="int" remarks="容量基类ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseTypeName" type="varchar(64)" remarks="容量基类名称">
                <constraints nullable="true"/>
            </column>

            <column name="Description" type="varchar(128)" remarks="描述">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="CapacityBaseType"
                          columnName="BaseTypeId"
                          incrementBy="1"
                          columnDataType="int"
                          startWith="1000000"/>
    </changeSet>
</databaseChangeLog>