<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_thermal_runaway_control_policy_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ThermalRunawayControlPolicy"
                     remarks="thermal runaway control policy table">
            <column name="Id" type="int" remarks="自增Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BatteryEquipmentId" type="int" remarks="电池设备Id">
                <constraints nullable="false"/>
            </column>
            <column name="PhaseId" type="int" remarks="热失控阶段Id">
                <constraints nullable="false"/>
            </column>
            <column name="ControlPolicy" type="varchar(500)" remarks="控制策略">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ThermalRunawayControlPolicy" columnName="Id" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>