<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_batchcontrolrecord" author="hxh">
        <createTable tableName="Aircon_BatchControlRecord"
                     remarks="aircon batchcontrolrecord">
            <column name="RecordId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ControlId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="StdControlId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="StdWorkModeFlag" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="AirStdTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="AirCommon2No" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SerialNo" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Uuid" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="InsertTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Aircon_BatchControlRecord" columnName="RecordId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>