<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_dataitem" author="cy">
        <createTable tableName="Energy_DataItem"
                     remarks="basic energy dataitem map table">
            <column name="EntryItemId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ParentEntryId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EntryId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ItemId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ItemValue" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
			<column name="ItemAlias" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
			<column name="IsSystem" type="tinyint">
                <constraints nullable="true"/>
            </column>
			<column name="IsDefault" type="tinyint">
                <constraints nullable="true"/>
            </column>
			<column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
			<column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
			<column name="ExtendField2" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
			<column name="ExtendField3" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
			<column name="ExtendField4" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
			<column name="ExtendField5" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="CanEdit" type="tinyint">
                <constraints nullable="true"/>
            </column>
            <column name="CanDelete" type="tinyint">
                <constraints nullable="true"/>
            </column>
            <column name="canTimeliness" type="tinyint">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="Energy_DataItem" columnName="EntryItemId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>