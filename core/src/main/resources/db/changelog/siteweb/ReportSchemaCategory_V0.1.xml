<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_ReportSchemaCategory_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportSchemaCategory"
                     remarks="ReportSchemaCategory info table">
            <column name="ReportSchemaCategoryId" type="int" remarks="报表Schema分类ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportSchemaCategoryName" type="varchar(128)" remarks="报表Schema分类名称">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaCategoryDescription" type="varchar(128)" remarks="报表Schema分类备注">
                <constraints nullable="true"/>
            </column>
            <column name="ReportSchemaCategoryPath" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="SortVal" type="int" remarks="排序">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportSchemaCategory" columnName="ReportSchemaCategoryId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>