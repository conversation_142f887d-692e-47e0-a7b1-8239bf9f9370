<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="SafeMessageRecord" author="liaoximing"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="SafeMessageRecord" remarks="SafeMessageRecord info table">
            <column name="SafeMessageRecordId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SafeMessageId" type="int" remarks="平安短信配置id">
                <constraints nullable="false" />
            </column>
            <column name="SendContent" type="varchar(255)" remarks="发送内容">
                <constraints nullable="false" />
            </column>
            <column name="Receiver" type="varchar(255)" remarks="接收人">
                <constraints nullable="false" />
            </column>
            <column name="SendTime" type="datetime" remarks="发送时间">
                <constraints nullable="false" />
            </column>
            <column name="Remark" type="varchar(128)" remarks="备注">
                <constraints nullable="false" />
            </column>
        </createTable>
        <addAutoIncrement tableName="SafeMessageRecord" columnName="SafeMessageRecordId" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>