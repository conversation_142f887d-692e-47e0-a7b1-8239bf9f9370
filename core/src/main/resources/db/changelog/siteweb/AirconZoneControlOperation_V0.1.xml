<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_zonecontroloperation" author="hxh">
        <createTable tableName="Aircon_ZoneControlOperation"
                     remarks="aircon zonecontroloperation">

            <column name="OperationId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SchemeId" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="OperationTime" type="varchar(16)">
                <constraints nullable="false"/>
            </column>
            <column name="OperationCmdId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperationCmdName" type="varchar(64)">
                <constraints nullable="false"/>
            </column>
            <column name="Params" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Aircon_ZoneControlOperation" columnName="OperationId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>