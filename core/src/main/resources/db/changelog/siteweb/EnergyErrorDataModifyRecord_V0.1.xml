<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_EnergyErrorDataModifyRecord" author="lz">
        <createTable tableName="energy_errordatamodifyrecord"
                     remarks="energy_errordatamodifyrecord">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ComplexIndexId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ErrorTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="ModifyTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UserId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UserName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="OriginalValue" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="IndexValue" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="Note" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="energy_errordatamodifyrecord" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>