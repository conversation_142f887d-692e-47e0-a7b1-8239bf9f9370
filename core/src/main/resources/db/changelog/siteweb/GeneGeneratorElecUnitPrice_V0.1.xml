<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_gene_generatorelecunitprice" author="hxh">
        <createTable tableName="Gene_GeneratorElecUnitPrice" remarks="gene generatorelecunitprice">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentBaseType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="Year" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Month" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="YearMonth" type="varchar(16)">
                <constraints nullable="false"/>
            </column>
            <column name="ElecUnitPrice" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Gene_GeneratorElecUnitPrice" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="Gene_GeneratorElecUnitPrice" indexName="IDX_Gene_GeneratorElecUnitPrice_1" unique="true">
            <column name="EquipmentId"></column>
            <column name="Year"></column>
            <column name="Month"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>