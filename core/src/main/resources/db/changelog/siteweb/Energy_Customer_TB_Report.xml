<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="energy_customer_tb_report" author="cy">
        <createTable tableName="Energy_Customer_TB_Report"
                     remarks="basic energy energy_customer_tb_report table">
            <column name="ReportId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportName" type="varchar(256)">
                <constraints nullable="true"/>
            </column>
            <column name="Path" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="RecordPath" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
            <column name="Notes" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>