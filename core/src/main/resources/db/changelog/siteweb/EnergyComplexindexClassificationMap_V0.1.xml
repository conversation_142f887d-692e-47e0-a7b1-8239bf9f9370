<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_complexindexclassificationmap" author="hxh">
        <createTable tableName="Energy_ComplexIndexClassificationMap"
                     remarks="energy complexindex classification map">
            <column name="ResourceStructureId" type="int" >
                <constraints nullable="false" />
            </column>
            <column name="ComplexIndexId" type="int" >
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ClassificationId" type="int" >
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField1" type="varchar(128)" >
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>