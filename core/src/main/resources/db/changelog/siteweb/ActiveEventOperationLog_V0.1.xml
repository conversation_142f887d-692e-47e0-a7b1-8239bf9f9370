<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_ActiveEventOperationLog_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ActiveEventOperationLog" remarks="ActiveEventOperationLog info table">
            <column name="ActiveEventOperationLogId" type="bigint" remarks="主键ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SequenceId" type="VARCHAR(128)" remarks="流水号">
                <constraints nullable="false"/>
            </column>
            <column name="StationId" type="int" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="int" remarks="事件ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventConditionId" type="INT" remarks="事件条件ID">
                <constraints nullable="false"/>
            </column>
            <column name="StartTime" type="datetime" remarks="开始时间">
                <constraints nullable="false"/>
            </column>
            <column name="OperatorId" type="int" remarks="操作人ID">
                <constraints nullable="false"/>
            </column>
            <column name="Operation" type="varchar(128)" remarks="告警操作">
                <constraints nullable="false"/>
            </column>
            <column name="OperationTime" type="datetime" remarks="操作时间">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="备注">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ActiveEventOperationLog" columnName="ActiveEventOperationLogId" incrementBy="1" columnDataType="bigint" startWith="1"/>
        <createIndex tableName="ActiveEventOperationLog" indexName="IDX_ActiveEventOperationLog_SequenceId">
            <column name="SequenceId"></column>
        </createIndex>
        <createIndex tableName="ActiveEventOperationLog" indexName="ActiveEventOperationLog_IDX1">
            <column name="StartTime"></column>
            <column name="EquipmentId"></column>
            <column name="EventId"></column>
            <column name="StationId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>