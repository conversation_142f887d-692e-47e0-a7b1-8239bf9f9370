<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_gene_refuelrecord" author="hxh">
        <createTable tableName="Gene_RefuelRecord" remarks="gene refuelrecord">

            <column name="SerialId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="OilId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OilName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="RefuelTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="RefuelQuantity" type="double">
                <constraints nullable="false"/>
            </column>
            <column name="UnitPrice" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="RefuelFee" type="double">
                <constraints nullable="true"/>
            </column>
            <column name="Operator" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="UpdaterId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Extend1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Gene_RefuelRecord" columnName="SerialId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="Gene_RefuelRecord" indexName="IDX_Gene_RefuelRecord_1">
            <column name="OilId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>