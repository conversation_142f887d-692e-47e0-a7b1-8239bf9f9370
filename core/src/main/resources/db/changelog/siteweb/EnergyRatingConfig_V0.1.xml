<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_ratingconfig" author="lqp" >
        <createTable tableName="energy_ratingconfig"
                     remarks="">

            <column name="RatingConfigId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="OutDryTempParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="OutWetTempParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="InDryTempParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="InWetTempParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="RunningLoadParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ITPowerParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="TotalPowerParam" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Name" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="IntervalSecond" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Status" type="int" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="EndTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="WorkingCondition" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="UserId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="CityId" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="energy_ratingconfig" columnName="RatingConfigId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>