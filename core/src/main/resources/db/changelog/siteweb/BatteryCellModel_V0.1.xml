<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_batteryCellModel_item" author="habits" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="BatteryCellModel"
                     remarks="BatteryCellModel table">
            <column name="BatteryCellModelId" type="int" remarks="电池模型ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="Vendor" type="varchar(128)" remarks="电池厂商">
                <constraints nullable="true"/>
            </column>
            <column name="Model" type="varchar(255)" remarks="电池型号">
                <constraints nullable="true"/>
            </column>
            <column name="VoltageType" type="int" remarks="电压类型">
                <constraints nullable="false"/>
            </column>
            <column name="RatedVoltage" type="decimal(5,2)" remarks="额定电压">
                <constraints nullable="true"/>
            </column>
            <column name="RatedCapacity" type="decimal(6,2)" remarks="额定容量">
                <constraints nullable="true"/>
            </column>
            <column name="InitialIR" type="decimal(10,2)" remarks="初始内阻">
                <constraints nullable="true"/>
            </column>
            <column name="FloatChargeVoltage" type="decimal(5,2)" remarks="浮充电压">
                <constraints nullable="true"/>
            </column>
            <column name="EvenChargeVoltage" type="decimal(5,2)" remarks="均充电压">
                <constraints nullable="true"/>
            </column>
            <column name="TempCompensationFactor" type="decimal(3,2)" remarks="温度补偿系数">
                <constraints nullable="true"/>
            </column>
            <column name="TerminationVoltage" type="decimal(5,2)" remarks="终止电压">
                <constraints nullable="true"/>
            </column>
            <column name="ChargingCurrentLimitFactor" type="decimal(5,2)" remarks="充电限流系数">
                <constraints nullable="true"/>
            </column>
            <column name="DischargeCurrentLimitFactor" type="decimal(5,2)" remarks="放电限流系数">
                <constraints nullable="true"/>
            </column>
            <column name="Length" type="int" remarks="长度">
                <constraints nullable="true"/>
            </column>
            <column name="Width" type="int" remarks="宽度">
                <constraints nullable="true"/>
            </column>
            <column name="Height" type="int" remarks="高度">
                <constraints nullable="true"/>
            </column>
            <column name="Weight" type="decimal(10,2)" remarks="重量">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="BatteryCellModel" columnName="BatteryCellModelId" incrementBy="1"
                          columnDataType="int" startWith="100"/>
    </changeSet>
</databaseChangeLog>