<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_pd_prealarmreportrecord" author="hxh">
        <createTable tableName="PD_PrealarmReportRecord"
                     remarks="pd prealarm report record">
            <column name="RecordId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PreAlarmId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="PreAlarmPointId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="PreAlarmSeverity" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="PreAlarmCategory" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ObjectTypeId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="TriggerValue" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ReportPath" type="varchar(512)">
                <constraints nullable="true"/>
            </column>
            <column name="ReportFileName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="HistoryDataPath" type="varchar(512)">
                <constraints nullable="true"/>
            </column>
            <column name="HistoryDataFileName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="InsertTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="GenerateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="GenerateResult" type="int" remarks="生成结果：0-等待生成中；1-生成成功；2-生成失败；3-已删除；-1及其他-未知状态">
                <constraints nullable="false"/>
            </column>
            <column name="DeleteTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="PD_PrealarmReportRecord" columnName="RecordId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="PD_PrealarmReportRecord" indexName="IDX_PD_PrealarmReportRecord_1" unique="true" >
            <column name="PreAlarmId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>