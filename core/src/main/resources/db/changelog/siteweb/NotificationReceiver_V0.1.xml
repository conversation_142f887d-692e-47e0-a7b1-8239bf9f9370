<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_notificationReceiver_info" author="lzy">
        <createTable tableName="NotificationReceiver" remarks="消息通知接收记录">
            <column name="NotificationReceiverId" type="int" remarks="主键">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="NotificationId" type="int" remarks="消息通知id">
                <constraints nullable="false"/>
            </column>
            <column name="LoginUserId" type="int" value="用户id">
                <constraints nullable="false"/>
            </column>
            <column name="Readed" type="tinyint(1)" remarks="是否已读">
                <constraints nullable="true"/>
            </column>
            <column name="ReadedTime" type="datetime" remarks="已读时间">
                <constraints nullable="true"/>
            </column>
            <column name="Deleted" type="tinyint(1)" remarks="是否删除">
                <constraints nullable="true"/>
            </column>
            <column name="DeletedTime" type="datetime" remarks="删除时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="NotificationReceiver" columnName="NotificationReceiverId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>