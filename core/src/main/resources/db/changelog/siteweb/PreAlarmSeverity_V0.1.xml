<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_PreAlarmSeverity_info" author="xueyi">
        <createTable tableName="PreAlarmSeverity" remarks="PreAlarmSeverity info table">
            <column name="PreAlarmSeverityId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PreAlarmSeverityName" type="varchar(64)">
                <constraints nullable="false" />
            </column>
            <column name="Color" type="varchar(128)">
                <constraints nullable="true" />
            </column>
            <column name="Description" type="varchar(128)">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="PreAlarmSeverity" columnName="PreAlarmSeverityId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>