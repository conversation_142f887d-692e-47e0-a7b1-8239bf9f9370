<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_EnergyConsumeConst" author="hxh">
        <createTable tableName="Energy_ConsumeConst"
                     remarks="">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ObjectId" type="varchar(45)">
                <constraints nullable="false" />
            </column>
            <column name="ObjectTypeId" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="Peoples" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Area" type="float">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ConsumeConst" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>