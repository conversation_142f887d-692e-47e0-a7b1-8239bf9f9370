<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_EnergyConsumeData" author="hxh">
        <createTable tableName="Energy_ConsumeData"
                     remarks="">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ObjectId" type="varchar(45)">
                <constraints nullable="false" />
            </column>
            <column name="ObjectTypeId" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="EnergyTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="year" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="month" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EnergySavingValue" type="float">
                <constraints nullable="true"/>
            </column>
            <column name="PlanValue" type="float">
                <constraints nullable="true"/>
            </column>
            <column name="OverstepValue" type="float">
                <constraints nullable="true"/>
            </column>
            <column name="PlanValuePreAlarm" type="float">
                <constraints nullable="true"/>
            </column>
            <column name="OverstepValuePreAlarm" type="float">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_ConsumeData" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>