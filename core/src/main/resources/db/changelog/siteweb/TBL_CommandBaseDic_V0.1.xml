<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

<changeSet id="create_TBL_CommandBaseDic_info" author="lichunling" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_CommandBaseDic" remarks="TBL_CommandBaseDic info table">
            <column name="BaseTypeId" type="bigint" remarks="控制基类ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BaseTypeName" type="varchar(128)" remarks="控制基类名">
                <constraints nullable="false"/>
            </column>
            <column name="BaseEquipmentId" type="int" remarks="基类设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EnglishName" type="varchar(256)" remarks="控制基类名-英文">
                <constraints nullable="true"/>
            </column>
            <column name="BaseLogicCategoryId" type="int"  remarks="控制逻辑分类">
                <constraints nullable="true"/>
            </column>
            <column name="CommandType" type="int" remarks="控制类型">
                <constraints nullable="false"/>
            </column>
            <column name="BaseStatusId" type="int" remarks="基类状态Id">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(256)" remarks="扩展字段1">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField2" type="varchar(256)" remarks="扩展字段2">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField3" type="varchar(256)" remarks="扩展字段3">
                <constraints nullable="true"/>
            </column>
            <column name="BaseNameExt" type="varchar(128)" remarks="序号扩展">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(256)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
            <column name="IsSystem" type="tinyint(1)" defaultValue="1" remarks="是否系统默认">
                <constraints nullable="false"  />
            </column>
        </createTable>
    <createIndex tableName="TBL_CommandBaseDic" indexName="idx_CommandBaseDic_EquipmentId_Id">
        <column name="BaseEquipmentId"></column>
    </createIndex>
    </changeSet>
</databaseChangeLog>