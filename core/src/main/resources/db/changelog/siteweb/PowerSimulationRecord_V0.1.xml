<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_PowerSimulationRecord" author="lz">
        <createTable tableName="powersimulationrecord"
                     remarks="powersimulationrecord">
            <column name="recordId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="recordName" type="varchar(225)">
                <constraints nullable="false"/>
            </column>
            <column name="diagramId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="nodeId" type="varchar(225)">
                <constraints nullable="false"/>
            </column>
            <column name="onOffState" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="downloadPath" type="varchar(225)">
                <constraints nullable="false"/>
            </column>
            <column name="attributeObjects" type="text">
                <constraints nullable="false"/>
            </column>
            <column name="reportName" type="varchar(225)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="powersimulationrecord" columnName="recordId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>