<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="AlarmNotifyGatewayService" author="yjy" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmNotifyGatewayService" remarks="AlarmNotifyGatewayService info table">
            <column name="AlarmNotifyGatewayServiceId" type="int" remarks="主键id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ElementId" type="int" remarks="发送方式id">
                <constraints nullable="false" />
            </column>
            <column name="GatewayServiceUrl" type="varchar(64)" remarks="网关服务url">
                <constraints nullable="true" />
            </column>
            <column name="Description" type="varchar(256)" remarks="备注">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmNotifyGatewayService" columnName="AlarmNotifyGatewayServiceId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>