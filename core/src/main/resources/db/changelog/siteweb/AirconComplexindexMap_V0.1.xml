<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_complexindexmap" author="hxh">
        <createTable tableName="Aircon_ComplexindexMap"
                     remarks="aircon complexindexmap">

            <column name="MapId" type="varchar(255)" remarks="UUID PK">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="VirtualEquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ComplexIndexId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>