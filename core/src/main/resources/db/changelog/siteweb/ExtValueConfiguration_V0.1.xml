<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_extValueConfiguration_info" author="lzy">
        <createTable tableName="ExtValueConfiguration" remarks="扩展字段值表">
            <column name="ExtValId" type="int" remarks="主键">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ExtId" type="int" remarks="扩展字段id">
                <constraints nullable="false"/>
            </column>
            <column name="ExtTable" type="varchar(50)" remarks="扩展字段值关联表">
                <constraints nullable="false"/>
            </column>
            <column name="ExtTablePkId" type="int" remarks="扩展字段关联表主键id">
                <constraints nullable="false"/>
            </column>
            <column name="ExtValue" type="varchar(1000)" remarks="扩展字段值">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ExtValueConfiguration" columnName="ExtValId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>