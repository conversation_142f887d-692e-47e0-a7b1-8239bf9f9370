<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_complexIndexBusinessObjectMap_info" author="psx">
        <createTable tableName="ComplexIndexBusinessObjectMap" remarks="ComplexIndexBusinessObjectMap info table">
            <column name="BusinessObjectMapId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SceneId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="BusinessTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectTypeId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ComplexIndexDefinitionId" type="int">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ComplexIndexBusinessObjectMap" columnName="BusinessObjectMapId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>