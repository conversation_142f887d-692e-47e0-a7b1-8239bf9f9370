<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_complexIndexBusinessType_info" author="psx">
        <createTable tableName="ComplexIndexBusinessType" remarks="complexIndexBusinessType info table">
            <column name="BusinessTypeId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BusinessTypeName" type="varchar(50)">
                <constraints nullable="false"/>
            </column>
            <column name="ParentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ComplexIndexBusinessType" columnName="BusinessTypeId" incrementBy="1" columnDataType="int" startWith="1"/>

    </changeSet>
</databaseChangeLog>