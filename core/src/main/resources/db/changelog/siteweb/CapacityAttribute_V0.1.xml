<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_capacity_attribute_info" author="liaoximing">
        <createTable tableName="CapacityAttribute" remarks="容量属性信息表">

            <column name="AttributeId" type="int" remarks="容量id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>

            <column name="BaseTypeId" type="int" remarks="容量基类id">
                <constraints nullable="false"/>
            </column>

            <column name="BaseAttributeId" type="int" remarks="容量父级id">
                <constraints nullable="false"/>
            </column>

            <column name="AttributeName" type="varchar(64)" remarks="容量名称">
                <constraints nullable="true"/>
            </column>

            <column name="LogicType" type="int" remarks="计算方式 0被动更新 1指标计算">
                <constraints nullable="false"/>
            </column>

            <column name="Description" type="varchar(128)" remarks="描述">
                <constraints nullable="true"/>
            </column>

            <column name="ObjectId" type="int" remarks="对象id">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectTypeId" type="int" remarks="对象资源类型id">
                <constraints nullable="false"/>
            </column>

            <column name="ComplexIndex" type="int" remarks="指标ID（不可修改）">
                <constraints nullable="true"/>
            </column>

            <column name="MinValue" type="double" defaultValue="0" remarks="最小值">
                <constraints nullable="true"/>
            </column>

            <column name="MaxValue" type="double" defaultValue="100" remarks="最大值">
                <constraints nullable="false"/>
            </column>

            <column name="RatedCapacity" type="double" defaultValue="100" remarks="额定容量">
                <constraints nullable="true"/>
            </column>

            <column name="DefaultValue" type="double" defaultValue="0" remarks="默认值">
                <constraints nullable="true"/>
            </column>

            <column name="CompensateFactor" type="double" defaultValue="1" remarks="补偿因子">
                <constraints nullable="true"/>
            </column>

            <column name="OriginCapacity" type="double" defaultValue="0" remarks="原始输入数据">
                <constraints nullable="true"/>
            </column>

            <column name="UsedCapacity" type="double" defaultValue="0" remarks="已使用的容量 计算公式: 原生数据 * 补偿因子 (originCapacity * compensateFactor)">
                <constraints nullable="true"/>
            </column>

            <column name="FreeCapacity" type="double" defaultValue="0" remarks="空闲容量">
                <constraints nullable="true"/>
            </column>

            <column name="Percent" type="double" defaultValue="0" remarks="容量百分比">
                <constraints nullable="true"/>
            </column>

            <column name="SampleTime" type="varchar(20)" remarks="更新时间">
                <constraints nullable="true"/>
            </column>

            <column name="Unit" type="varchar(16)" defaultValue="%" remarks="单位">
                <constraints nullable="true"/>
            </column>

            <column name="Precision" type="int" defaultValue="2" remarks="精度">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="CapacityAttribute"
                          columnName="AttributeId"
                          incrementBy="1"
                          columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>