<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

     <changeSet id="create_tblControlmeanings_info" author="williams_wu" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_ControlMeanings" remarks="tblControlmeanings info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EquipmentTemplateId" type="int" remarks="设备模板ID">
                <constraints nullable="false"/>
            </column>
            <column name="ControlId" type="int" remarks="控制命令ID">
                <constraints nullable="false"/>
            </column>
            <column name="ParameterValue" type="smallint" remarks="控制值">
                <constraints nullable="false"/>
            </column>
            <column name="Meanings" type="varchar(255)" remarks="命令含义">
                <constraints nullable="true"/>
            </column>
            <column name="BaseCondId" type="decimal" remarks="基类ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_ControlMeanings" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
         <createIndex tableName="TBL_ControlMeanings" indexName="idx_ControlMeanings_Id" unique="false">
             <column name="EquipmentTemplateId"></column>
             <column name="ControlId"></column>
         </createIndex>
    </changeSet>
</databaseChangeLog>