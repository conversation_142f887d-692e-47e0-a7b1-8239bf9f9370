<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_zonecontrolscheme" author="hxh">
        <createTable tableName="Aircon_ZoneControlScheme"
                     remarks="aircon zonecontrolscheme">

            <column name="SchemeId" type="varchar(255)" remarks="UUID PK">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SchemeName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="StartDate" type="varchar(16)">
                <constraints nullable="false"/>
            </column>
            <column name="EndDate" type="varchar(16)">
                <constraints nullable="false"/>
            </column>
            <column name="VirtualEquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>