<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_management_map" author="cy">
        <createTable tableName="Energy_ManagementMap"
                     remarks="basic energy management map table">
            <column name="MapId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EnergyId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ResourceStructureId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="IsMarker" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Property" type="varchar(1000)">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="Energy_ManagementMap" columnName="MapId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="Energy_ManagementMap" indexName="Energy_ManagementMap_IDX1" unique="false">
            <column name="EnergyId"></column>
            <column name="ResourceStructureId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>