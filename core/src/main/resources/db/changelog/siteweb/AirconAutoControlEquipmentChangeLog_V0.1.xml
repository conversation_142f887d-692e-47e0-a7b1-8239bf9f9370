<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_autocontrolequipmentchangelog" author="hxh">
        <createTable tableName="Aircon_AutoControlEquipmentChangeLog"
                     remarks="aircon autocontrolequipment changeLog">

            <column name="LogId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="StationName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="MonitorUnitId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="MonitorUnitName" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="VirtualEquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="GroupName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="GroupNameNew" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="OperateType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperateTypeLabel" type="varchar(16)">
                <constraints nullable="false"/>
            </column>
            <column name="OperateModule" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperateModuleLabel" type="varchar(64)">
                <constraints nullable="false"/>
            </column>
            <column name="SubOperateType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="SubOperateTypeLabel" type="varchar(16)">
                <constraints nullable="true"/>
            </column>
            <column name="SubObjectType" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="SubObjectTypeLabel" type="varchar(64)">
                <constraints nullable="true"/>
            </column>
            <column name="Operator" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="OperatorId" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="InsertTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="ChangeContent" type="varchar(4000)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Aircon_AutoControlEquipmentChangeLog" columnName="LogId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>