<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_scenepermissionmap_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ScenePermissionMap" remarks="scenepermissionmap info table">
            <column name="ScenePermissionMapId" type="int" remarks="场景权限点唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="PermissionId" type="int" remarks="权限点ID">
                <constraints nullable="false"/>
            </column>
            <column name="SceneId" type="int" remarks="场景ID">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ScenePermissionMap" columnName="ScenePermissionMapId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>