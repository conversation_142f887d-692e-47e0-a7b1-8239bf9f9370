<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="DimensionConfigure" author="liaoximing"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="DimensionConfigure" remarks="DimensionConfigure info table">
            <column name="DimensionConfigureId" type="int" remarks="主键ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="DimensionConfigureType" type="int" remarks="资源类型">
                <constraints nullable="true" />
            </column>
            <column name="DimensionConfigure" type="mediumtext" remarks="可变的的配置项">
                <constraints nullable="true" />
            </column>
            <column name="DimensionConfigureUuid" type="varchar(128)" remarks="唯一uuid">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="DimensionConfigure" columnName="DimensionConfigureId" incrementBy="1"
                          columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>