<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_thermal_runaway_advice_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ThermalRunawayAdvice"
                     remarks="thermal runaway advice table">
            <column name="Id" type="int" remarks="热失控处置建议Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AdviceType" type="varchar(128)" remarks="建议类别">
                <constraints nullable="true"/>
            </column>
            <column name="Reason" type="varchar(500)" remarks="原因">
                <constraints nullable="true"/>
            </column>
            <column name="Advice" type="varchar(500)" remarks="处理建议">
                <constraints nullable="true"/>
            </column>
            <column name="Contacts" type="varchar(128)" remarks="联系人">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ThermalRunawayAdvice" columnName="Id" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>