<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_eventconvergencerule_info" author="wiliam"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="EventConvergenceRule"
                     remarks="EventConvergenceRule info table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RuleName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="ConvergenceType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentCategory" type="int">
                <constraints nullable="false" />
            </column>
            <column name="StartExpression" type="varchar(1024)">
                <constraints nullable="true" />
            </column>
            <column name="FilterCondition" type="varchar(2048)">
                <constraints nullable="true" />
            </column>
            <column name="ConvergenceInterval" type="int">
                <constraints nullable="true" />
            </column>
            <column name="StartCount" type="int">
                <constraints nullable="true" />
            </column>
            <column name="EndCount" type="int">
                <constraints nullable="true" />
            </column>
            <column name="PossibleCauses" type="varchar(1024)">
                <constraints nullable="true" />
            </column>
            <column name="ParentRuleId" type="int">
                <constraints nullable="true" />
            </column>
            <column name="LevelOfPath" type="varchar(1024)">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="EventConvergenceRule" columnName="Id" incrementBy="1"  columnDataType ="int" startWith="1" />
    </changeSet>
</databaseChangeLog>