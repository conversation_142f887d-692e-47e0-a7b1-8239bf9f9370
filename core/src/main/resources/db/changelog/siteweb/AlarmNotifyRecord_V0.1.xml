<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_alarm_notify_record_info" author="vincent" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="AlarmNotifyRecord"
                     remarks="alarm notify record table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="AlarmNotifyConfigId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EventConditionId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="SequenceId" type="varchar(128)">
                <constraints nullable="false"/>
            </column>
            <column name="EventSeverityId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Content" type="varchar(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="AlarmStartTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="SendTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="Receiver" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="SendType" type="varchar(20)">
                <constraints nullable="false"/>
            </column>
            <column name="SendResult" type="varchar(100)">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="AlarmNotifyRecord" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>