<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="HistoryPassword_table" author="william"   objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="HistoryPassword"
                     remarks="历史密码表">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="LogonId" type="varchar(128)" remarks="登录名">
                <constraints nullable="false" />
            </column>
            <column name="Password" type="varchar(128)" remarks="密码">
                <constraints nullable="true"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更改时间">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="HistoryPassword" columnName="Id" incrementBy="1"  columnDataType ="bigint" startWith="1" />
    </changeSet>
</databaseChangeLog>