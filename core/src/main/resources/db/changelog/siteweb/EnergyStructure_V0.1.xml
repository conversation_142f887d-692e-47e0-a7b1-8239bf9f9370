<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_structure" author="hxh">
        <createTable tableName="Energy_Structure"
                     remarks="energy structure">

            <column name="StructureId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="StructureName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="SourceCategory" type="int">
                <constraints nullable="false" />
            </column>
            <column name="Notes" type="varchar(1000)">
                <constraints nullable="false"/>
            </column>
            <column name="AsRootInflowNode" type="int">
                <constraints nullable="true" />
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_Structure" columnName="StructureId"
                          incrementBy="1" columnDataType="int" startWith="10000000"/>
    </changeSet>
</databaseChangeLog>