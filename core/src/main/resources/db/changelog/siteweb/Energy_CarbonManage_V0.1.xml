<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_EnergyCarbonManage" author="lz">
        <createTable tableName="Energy_carbonmanage"
                     remarks="Energy_carbonmanage">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ObjectTypeId" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="ObjectId" type="varchar(45)">
                <constraints nullable="false"/>
            </column>
            <column name="Year" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Month" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="PlanValue" type="float">
                <constraints nullable="false"/>
            </column>
            <column name="YearPlanTotalValue" type="float">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_carbonmanage" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>