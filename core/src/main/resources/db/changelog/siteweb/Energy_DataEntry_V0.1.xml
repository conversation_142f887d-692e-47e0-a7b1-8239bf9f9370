<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_dataentry" author="hxl">
        <createTable tableName="Energy_DataEntry"
                     remarks="basic energy dataentry table">
            <column name="EntryId" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="EntryName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="EntryAlias" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="CanEdit" type="bit(1)">
                <constraints nullable="true"/>
            </column>
            <column name="CanDelete" type="bit(1)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="Energy_DataEntry" columnName="EntryId"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>