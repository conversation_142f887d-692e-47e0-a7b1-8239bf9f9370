<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_TBL_EquipmentBranch_info" author="liaoximing"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TBL_EquipmentBranch" remarks="TBL_EquipmentBranch info table">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="BranchId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="BranchName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentName" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_EquipmentBranch" columnName="id" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="TBl_EquipmentBranch" indexName="IDX_EquipmentBranch_EquipmentId_BranchId">
            <column name="EquipmentId"/>
            <column name="BranchId"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>