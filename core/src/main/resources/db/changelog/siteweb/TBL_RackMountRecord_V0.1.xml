<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_RackMountRecord_info" author="Liu.Yandong">
        <createTable tableName="TBL_RackMountRecord" remarks="Digitalor Rack Mount Record">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="RackId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="RackDeviceId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="RackPosition" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="OperateTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="OperateState" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="Expired" type="tinyint(1)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TBL_RackMountRecord" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>

    <changeSet author="Liu.Yandong" id="create_RackMountRecord_index">
        <createIndex tableName="TBL_RackMountRecord" indexName="rackmountrecord_expired_index">
            <column name="Expired"/>
        </createIndex>
    </changeSet>
</databaseChangeLog>