<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_thermal_runaway_affect_device_info" author="vincent"
               objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ThermalRunawayAffectDevice"
                     remarks="thermal runaway affect device table">
            <column name="Id" type="int" remarks="自增Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="UPSEquipmentId" type="int" remarks="UPS设备Id">
                <constraints nullable="true"/>
            </column>
            <column name="AffectDeviceName" type="varchar(128)" remarks="受影响设备名称">
                <constraints nullable="true"/>
            </column>
            <column name="AffectDevicePosition" type="varchar(128)" remarks="受影响设备位置">
                <constraints nullable="true"/>
            </column>
            <column name="AffectDevicePurpose" type="varchar(255)" remarks="受影响设备用途">
                <constraints nullable="true"/>
            </column>
            <column name="AffectDevicePower" type="varchar(255)" remarks="受影响设备功率">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ThermalRunawayAffectDevice" columnName="Id" incrementBy="1" columnDataType="int"
                          startWith="1"/>
    </changeSet>
</databaseChangeLog>