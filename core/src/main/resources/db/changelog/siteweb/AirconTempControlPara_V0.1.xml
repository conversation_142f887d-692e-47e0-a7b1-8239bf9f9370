<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_aircon_tempcontrolpara" author="hxh">
        <createTable tableName="Aircon_TempControlPara"
                     remarks="aircon tempcontrolpara">

            <column name="VirtualEquipmentId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_TempControlPara_Table"/>
            </column>
            <column name="EquipmentId" type="int">
                <constraints nullable="false" primaryKey="true" primaryKeyName="PK_TempControlPara_Table"/>
            </column>
            <column name="StationId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="MonitorUnitId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="InsertTime" type="datetime">
                <constraints nullable="true"/>
            </column>
            <column name="Operationor" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="BeOutTemp" type="int">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>