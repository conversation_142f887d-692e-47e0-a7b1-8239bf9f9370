<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_resourceStructureType_info" author="williams"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ResourceStructureType" remarks="resourceStructureType info table" >
            <column name="ResourceStructureTypeId" type="int"  remarks="分类Id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SceneId" type="int" remarks="场景ID">
                <constraints nullable="true"/>
            </column>
            <column name="ResourceStructureTypeName" type="varchar(128)" remarks="分类名">
                <constraints nullable="false"/>
            </column>

            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>