<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_tblDriverTemplate_info" author="leizhiyi">
        <!-- 驱动结构模板 -->
        <createTable tableName="tbl_drivestructuretemplate" remarks="tbl_drivestructuretemplate info table (驱动结构模板表)">
            <column name="Id" type="int" remarks="驱动结构模板id">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="filePath" type="varchar(100)" remarks="结构路径">
                <constraints nullable="false"/>
            </column>
            <column name="pid" type="int" remarks="上级结构id" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="fileId" type="bigint" remarks="模板文件id">
                <constraints nullable="true"/>
            </column>
            <column name="isDisk" type="tinyint(1)" remarks="是否是目录（默认是）" defaultValue="1">
                <constraints nullable="false"/>
            </column>
            <column name="isUpload" type="tinyint(1)" remarks="是否需要上传" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="isFill" type="tinyint(1)" remarks="是否需要填充" defaultValue="0">
                <constraints nullable="false"/>
            </column>
            <column name="driveTemplateId" type="int" remarks="驱动模板id">
                <constraints nullable="true"/>
            </column>
            <column name="isLeaf" type="tinyint(1)" remarks="是否是叶子节点">
                <constraints nullable="false"/>
            </column>
            <column name="uploadTiming" type="tinyint(1)" remarks="上传时机 0-模板创建时 1-生成配置时" defaultValue="0">
                <constraints nullable="false"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="tbl_drivestructuretemplate" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>