<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_reportTimingTaskManagement_info" author="psx" objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="ReportTimingTaskManagement" remarks="reportTimingTaskManagement info table">
            <column name="ReportTimingTaskManagementId" type="int" remarks="报表定时任务管理ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ReportTimingTaskManagementName" type="varchar(256)" remarks="报表定时任务管理名称">
                <constraints nullable="true" />
            </column>
            <column name="ReportId" type="int" remarks="报表ID">
                <constraints nullable="false"/>
            </column>
            <column name="ReportName" type="varchar(256)" remarks="报表名称">
                <constraints nullable="true" />
            </column>
            <column name="StorageCycle" type="varchar(64)" remarks="存储cron">
                <constraints nullable="false"/>
            </column>
            <column name="StartTimeType" type="varchar(64)" remarks="开始时间类型">
                <constraints nullable="true"/>
            </column>
            <column name="EndTimeType" type="varchar(64)" remarks="结束时间类型">
                <constraints nullable="true"/>
            </column>
            <column name="Status" type="tinyint(1)" remarks="状态">
                <constraints nullable="false"/>
            </column>
            <column name="to" type="varchar(1024)" remarks="收件人">
                <constraints nullable="true" />
            </column>
            <column name="cc" type="varchar(1024)" remarks="抄送">
                <constraints nullable="true" />
            </column>
            <column name="CreateUserId" type="int" remarks="创建报表的用户id">
                <constraints nullable="true"/>
            </column>
            <column name="Overt" type="tinyint" remarks="是否公开" defaultValue="1">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="ReportTimingTaskManagement" columnName="ReportTimingTaskManagementId" incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>


</databaseChangeLog>