<?xml version="1.0" encoding="UTF-8"?>
<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">
    <changeSet id="create_EnergyManualRecord" author="lz">
        <createTable tableName="energy_manualrecord"
                     remarks="energy_manualrecord">
            <column name="Id" type="int">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="ComplexIndexId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="RecordTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="RecordValue" type="float">
                <constraints nullable="false"/>
            </column>
            <column name="CalcType" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UserId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="UserName" type="varchar(255)">
                <constraints nullable="false"/>
            </column>
            <column name="InsertTime" type="datetime">
                <constraints nullable="false"/>
            </column>
            <column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="Unit" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="energy_manualrecord" columnName="Id"
                          incrementBy="1" columnDataType="int" startWith="1"/>
    </changeSet>
</databaseChangeLog>