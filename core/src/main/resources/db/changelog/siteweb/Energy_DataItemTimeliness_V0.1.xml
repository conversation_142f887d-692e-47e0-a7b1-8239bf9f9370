<?xml version="1.0" encoding="UTF-8"?>

<databaseChangeLog
        xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
         http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-3.1.xsd">

    <changeSet id="create_energy_dataitemtimeliness" author="cy">
        <createTable tableName="Energy_DataItemtimeliness"
                     remarks="basic energy dataitemtimeliness map table">
            <column name="ItemTimelinessId" type="int">
                <constraints nullable="false" primaryKey="true"/>
            </column>
            <column name="EntryItemId" type="int">
                <constraints nullable="false"/>
            </column>
            <column name="ItemValue" type="varchar(128)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField1" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField2" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField3" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField4" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="ExtendField5" type="varchar(255)">
                <constraints nullable="true"/>
            </column>
            <column name="StartTime" type="datetime">
                <constraints nullable="true"/>
            </column>
        </createTable>

        <addAutoIncrement tableName="Energy_DataItemtimeliness" columnName="ItemTimelinessId"
                          incrementBy="1" columnDataType="int" startWith="1"/>

        <addUniqueConstraint constraintName="Energy_DataItemtimeliness_unique"
                             tableName="Energy_DataItemtimeliness"
                             columnNames="EntryItemId, StartTime"/>
    </changeSet>
</databaseChangeLog>