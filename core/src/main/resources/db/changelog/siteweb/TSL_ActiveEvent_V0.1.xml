<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet author="williams_wu" id="tsl activeevent"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="tsl_activeevent" remarks="活动告警表">
            <column name="StationId" type="INT" remarks="局站ID">
                <constraints nullable="false"/>
            </column>
            <column name="EquipmentId" type="INT" remarks="设备ID">
                <constraints nullable="false"/>
            </column>
            <column name="EventId" type="INT" remarks="事件ID">
                <constraints nullable="false"/>
            </column>
             <column name="EventConditionId" type="INT" remarks="条件ID">
                <constraints nullable="false"/>
            </column>
            <column name="SequenceId" type="VARCHAR(128)" remarks="告警流水号">
                <constraints nullable="false" unique="true"/>
            </column>
            <column name="StartTime" type="datetime" remarks="开始时间">
                <constraints nullable="false"/>
            </column>
            <column name="EndTime" type="datetime" remarks="结束时间">
                <constraints nullable="true"/>
            </column>
            <column name="ResetSequenceId" type="VARCHAR(128)" remarks="重启包流水号">
                <constraints nullable="true"/>
            </column>
            <column name="EventValue" type="DOUBLE" remarks="触发值">
                <constraints nullable="true"/>
            </column>
            <column name="ReversalNum" type="INT" remarks="反转次数">
                <constraints nullable="true"/>
            </column>
            <column name="Meanings" type="VARCHAR(255)" remarks="告警涵义">
                <constraints nullable="true"/>
            </column>
            <column name="BaseTypeId" type="DECIMAL(12)" remarks="基类ID">
                <constraints nullable="true"/>
            </column>
        </createTable>
    </changeSet>
</databaseChangeLog>