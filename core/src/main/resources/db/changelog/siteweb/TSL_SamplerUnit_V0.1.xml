<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
                   xmlns:pro="http://www.liquibase.org/xml/ns/pro" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd http://www.liquibase.org/xml/ns/pro http://www.liquibase.org/xml/ns/pro/liquibase-pro-4.1.xsd http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.1.xsd">

    <changeSet id="create_tslSamplerunit_info" author="williams_wu"  objectQuotingStrategy="QUOTE_ONLY_RESERVED_WORDS">
        <createTable tableName="TSL_SamplerUnit" remarks="tslSamplerunit info table">
            <column name="Id" type="bigint" remarks="唯一ID">
                <constraints nullable="false" unique="true" primaryKey="true"/>
            </column>
            <column name="SamplerUnitId" type="int" remarks="采集单元ID">
                <constraints nullable="false"/>
            </column>
            <column name="PortId" type="int" remarks="端口ID">
                <constraints nullable="false"/>
            </column>
            <column name="MonitorUnitId" type="int" remarks="监控单元ID">
                <constraints nullable="false"/>
            </column>
            <column name="SamplerId" type="int" remarks="采集器ID">
                <constraints nullable="false"/>
            </column>
            <column name="ParentSamplerUnitId" type="int" remarks="父采集单元ID">
                <constraints nullable="false"/>
            </column>
            <column name="SamplerType" type="int" remarks="采集器类型">
                <constraints nullable="false"/>
            </column>
            <column name="SamplerUnitName" type="varchar(128)" remarks="采集单元名称">
                <constraints nullable="false"/>
            </column>
            <column name="Address" type="int" remarks="采集单元地址">
                <constraints nullable="false"/>
            </column>
            <column name="SpUnitInterval" type="double" remarks="采集周期">
                <constraints nullable="true"/>
            </column>
            <column name="DllPath" type="varchar(128)" remarks="采集动态库地址">
                <constraints nullable="true"/>
            </column>
            <column name="ConnectState" type="int" remarks="采集单元连接状态">
                <constraints nullable="false"/>
            </column>
            <column name="UpdateTime" type="datetime" remarks="更新时间">
                <constraints nullable="false"/>
            </column>
            <column name="PhoneNumber" type="varchar(20)" remarks="电话号码">
                <constraints nullable="true"/>
            </column>
            <column name="Description" type="varchar(255)" remarks="描述信息">
                <constraints nullable="true"/>
            </column>
        </createTable>
        <addAutoIncrement tableName="TSL_SamplerUnit" columnName="Id" incrementBy="1" columnDataType="int" startWith="1"/>
        <createIndex tableName="TSL_SamplerUnit" indexName="idx_SamplerUnit_Id" unique="true">
            <column name="MonitorUnitId"></column>
            <column name="PortId"></column>
            <column name="SamplerUnitId"></column>
        </createIndex>
    </changeSet>
</databaseChangeLog>