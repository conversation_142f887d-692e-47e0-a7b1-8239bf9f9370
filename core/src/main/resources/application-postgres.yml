spring:
  datasource:
    url: *****************************************************************************************************************************************************************************************************************************************************
    username: sysdba
    password: Vertiv@086
    driver-class-name: org.postgresql.Driver
  liquibase:
    change-log: classpath:db/changelog/siteweb/postgres/db.postgres.changelog-master.xml
    enabled: false
  quartz:
    properties:
      org:
        quartz:
          jobStore:
            driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
mybatis-plus:
  configuration:
    database-id: postgres
  mapper-locations: classpath*:mapper/postgres/**/*Mapper.xml
  # 用于注册自定义类型转换器
  type-handlers-package: com.siteweb.common.pgtypehandler
server:
  http:
    port: 8901
  port: 8900