spring:
  profiles:
    active: mysql
  datasource:
    hikari:
      pool-name: Retail_HikariCP
      minimum-idle: 5
      idle-timeout: 180000
      maximum-pool-size: 200
      auto-commit: true
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
      initialization-fail-timeout: 60000
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  main:
    banner-mode: console
  mvc:
    static-path-pattern: /**
    pathmatch:
      matching-strategy: ant_path_matcher
    servlet:
      load-on-startup: 1
    format:
      date: yyyy-MM-dd HH:mm:ss
  web:
    locale: zh_CN
    locale-resolver: fixed
    resources:
      static-locations: classpath:/META-INF/resources/,classpath:/resources/,classpath:/static/,classpath:/public/,classpath:/webui/
  servlet:
    multipart:
      #      单次请求文件大小设置
      max-file-size: 1024MB
      max-request-size: 1024MB
  messages:
    basename: i18n/messages
    cache-duration: -1s
    fallback-to-system-locale: false
  liquibase:
    enabled: false
  redis:
    database: 0
    host: **************
    port: 6479
    password: siteweb1!
    jedis:
      pool:
        max-active: 20
        max-wait: -1
        max-idle: 10
        min-idle: 0
      timeout: 1000
  siteweb-redis:
    database: 0
    host: **************
    port: 6379
    password: siteweb1!
  influx:
    url: http://**************:8086
    user: admin
    password: adminadmin
    connect-timeout: 3600
    read-timeout: 3600
    write-timeout: 3600
    database: siteweb_v2 #siteweb映射库，兼容1.x
    database2: siteweb5m_v2 #siteweb5m映射库，兼容1.x_v2
    database3: sitewebenergy_v2 #siteweb能耗数据映射库，兼容1.x
    database4: sitewebpd_v2 #siteweb配电快照数据映射库，兼容1.x
  data:
    mongodb:
      database: mymongo
      port: 27017
      host: **************
    web:
      pageable:
        max-page-size: 300000
  quartz:
    scheduler-name: clusteredScheduler # Scheduler 名字。默认为 schedulerName
    job-store-type: jdbc # Job 存储器类型。默认为 memory 表示内存，可选 jdbc 使用数据库。
    jdbc: # 使用 JDBC 的 JobStore 的时候，JDBC 的配置
      initialize-schema: never # 是否自动使用 SQL 初始化 Quartz 表结构。这里设置成 never ，我们手动创建表结构。
    auto-startup: true # Quartz 是否自动启动
    startup-delay: 0 # 延迟 N 秒启动
    wait-for-jobs-to-complete-on-shutdown: true # 应用关闭时，是否等待定时任务执行完成。默认为 false ，建议设置为 true
    overwrite-existing-jobs: false # 是否覆盖已有 Job 的配置
    properties: # 添加 Quartz Scheduler 附加属性，更多可以看 http://www.quartz-scheduler.org/documentation/2.4.0-SNAPSHOT/configuration.html 文档
      org:
        quartz:
          dataSource:
          #provider: hikaricp
          # JobStore 相关配置
          jobStore:
            # 数据源名称
            dataSource: quartzDataSource # 使用的数据源
            class: org.springframework.scheduling.quartz.LocalDataSourceJobStore # JobStore 实现类
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #双机情况下使用 com.siteweb.utility.quartz.delegate.DualHostDelegate 避免备机执行quartz任务
            tablePrefix: QRTZ_ # Quartz 表前缀
            isClustered: true # 是集群模式
            clusterCheckinInterval: 1000
            useProperties: false
          # 线程池相关配置
          threadPool:
            threadCount: 25 # 线程池大小。默认为 10 。
            threadPriority: 5 # 线程优先级
  #  kafka:
  #    bootstrap-servers: ************:9092,************:9092,************:9092
  #    retry:
  #      topic:
  #        attempts: 3
  #    properties:
  #      max.request.size: 10485760  # 设置 max.request.size
  #      security.protocol: SASL_PLAINTEXT
  #      sasl.mechanism: PLAIN
  #      sasl.jaas.config: org.apache.kafka.common.security.plain.PlainLoginModule required username='siteweb' password='Vertiv086';
  #      kafkaGroupId: SS
  #      kafkaMessageKey: 28
  # AD域认证相关配置
  ldap:
    urls: ldap://*************:389
    base: OU=test1,OU=test,OU=VET,DC=center,DC=com
    username: <EMAIL>
    password: 123456
    userbase: OU=test

  # 邮件服务相关配置
  mail:
    host: smtp.test.com
    port: 465
    username: <EMAIL>
    password: 123456789
    protocol: smtp
    defaultEncoding: UTF-8
    properties:
      mail:
        smtp:
          ssl:
            enable: true
          auth: true
          starttls:
            enable: true
            required: true
          socketFactory:
            port: 465
            class: javax.net.ssl.SSLSocketFactory
            fallback: false
  security:
    # 启用防重放攻击
    antiReplayAttack: false
    # 签名验证超时时间(分钟)
    signTimeout: 1
    # 允许未签名访问的url地址
    ignoreSignUri:
      - /swagger-ui.html
      - /swagger-resources
      - /v2/api-docs
      - /webjars/springfox-swagger-ui
      - /websocket
      - /doc.html
      - /csrf
      - /login
      - /error
      - /api/serverstatus
      - /api/checklicense
      - /api/files
      - /api/timestamp
      - /api/sso/login
      - /api/sso/logout
      - /api/keycloak/login
  rabbitmq:
    enable: false # 是否启用 RabbitMQ（需配置）
    host: ************** # rabbitmq 服务器id（需配置）
    port: 5673 #rabbitmq端口（需配置）
    virtual-host: / #交换机所在的虚拟主机（需配置）
    username: siteweb6 #rabbitmq账号（需配置）
    password: adminadmin #rabbitmq密码（需配置）
    exchange: user_exchange_park_master    #用户同步的fanout交换机（需配置）
    queue: s6_gy_user_sync #此处可不调整，如遇冲突可更换

mybatis-plus:
  #  不同数据库自己配置
  #  mapper-locations: classpath*:mapper/**/*Mapper.xml
  global-config:
    banner: false
  configuration:
    #    database-id: mysql
    mapUnderscoreToCamelCase: false
logging:
  config: classpath:logback-spring.xml

#security:
#  ignored: /**
#  basic:
#    enabled: false


jwt:
  privateKey: MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQC/DZ8QrtQaoyFwO/fBno3LguBv2CWsUqJlCjh0opI3jrDnI8bTIRGotKLrknT0GSEXw5CtakEAa1C8tudwJ5nL09UAvQXRAgH23d/P6HSQVjDwh/2VIf9j9hh6meJGH/7xFr4HRMyB/cVHgqwvmiCNZIbvCWbyijcTXr0VPkPzL4sWF2pgdvJZzmgKkAz0xcOHqUfqp5YsyEIRovX8xmp2qcBzvpxHZYkaYujv/cO7FNc9bOA6dhh5ayZu3MeAU313g72nqt/nA4yTWq27d0zkKfdy17ZhKA+vYvLJKlrv+8tO5UDpIpB0vQhKd7+S2w9r7tHVGfezUWCZgKxZ/LL3AgMBAAECggEBALK1FRLP1crMyJxpG4javJueYj18G1EjQo/sjX5cCxU4vbSXPIWEqzX5MWPU7NzfHJtT7OKpPwAbYbwEAlxgTnXgQZ+dL/GfRSMbyxx4vX+9f62eJs72rCNesOsNQiCCEUCGG15FNl5pd706N8GXE9fuLmEtlEROkNHnjkpuobS4JWhGGJci4mNQqw4ZgCOuUr5YZ4QA/Mb6BBAu6bcvu6es23oGB84ZcxpMjh69PSwfiYFFsppuiJflzjQjohtyv99MqstDRvrzPi+i/T3qZ2W9FRUuP1pf/dMaamg7PoTAoOgJjKbSo7aEgLZIo/SF7TKnjtB6SmBnlWxi7HLyKyECgYEA+PP0CbhnkcVolsCQtLwa4lL/3mOurjjtgCWYXBkYjbZ9vJgGWw2oJ+HUBNhpEa7ELrzjUvXumgqCnicDeUC/9QJDCf029ldGje9GkrHsOSbU6IHzzdGbmYHwgfnjB6vFwZx5VjvT2t3VBMYeet5HqrL33KD+WxRYziel6URSbKkCgYEAxHYYcw0ORtq57g8N7SakScNE2oVf+/1H3WqSUJDg+T38WEkxU9M6sqnJ9q2v9QSR2QynxS0SZKxvwJc4LzUbiHMP7v20xhBrA5+a8kzjPqKlM50p0C/Nkt7pSwFiX+LwFQlMUV9tWdY/Z6fBhKks9uxpEPuCbCjDlCg0T0iNRp8CgYEA7WinlwV2LztUrD7jQJgKAz8npsrk8Fx1kTlI/LsqASrA6bMIjJiPfckMSbqfKC/EAtY66wiBDAFt4qhN1bn71QjdKY+CdJVyQTSn1ok6Pp5bd4dGG0cC3fdehnTpHo2evy4bQDM5q4TU+gJ9WqrTKWQWnx4gsnbK4X5J6BQxjlECgYBxtZi5Hplg0UBEVVpOJMt6FhdIE2JWy2ZI9WHyV6ifGg1wXAy848lZl4RZznXFbvurkPOZ4FiBBH06D0xppmdlNpPGU/nJmb8Wvc5E59OvcRwFH7YP1Vs64uJMk2SI8yTaSCNwBbeZA7R3HlWXnwNzd6noNmpqh72LhymfqfJ7KQKBgEjDxUq7q3KU0KeYiitT3WGOHQ5kGmfEwNFjuxMqJohyyJ4Z3ajHbIbrm2TZ5Wq3Ims+XcQFiOhZ8UIIUiOldjgFPlL5sBWLyh2Q0MO8lvBH+AJVAsirmNSTHzCIcvGDLEHnppSnmBOtC89h6n8Hdcz2Ybd1kVuYzrVXBnodakL0
  publicKey: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAvw2fEK7UGqMhcDv3wZ6Ny4Lgb9glrFKiZQo4dKKSN46w5yPG0yERqLSi65J09BkhF8OQrWpBAGtQvLbncCeZy9PVAL0F0QIB9t3fz+h0kFYw8If9lSH/Y/YYepniRh/+8Ra+B0TMgf3FR4KsL5ogjWSG7wlm8oo3E169FT5D8y+LFhdqYHbyWc5oCpAM9MXDh6lH6qeWLMhCEaL1/MZqdqnAc76cR2WJGmLo7/3DuxTXPWzgOnYYeWsmbtzHgFN9d4O9p6rf5wOMk1qtu3dM5Cn3cte2YSgPr2LyySpa7/vLTuVA6SKQdL0ISne/ktsPa+7R1Rn3s1FgmYCsWfyy9wIDAQAB

server:
  http:
    port: 8701
  port: 8300
  ssl:
    enable: false
    key-store-password: siteweb1!
    key-store: classpath:s6-server.p12
    key-store-type: JKS
    key-alias: s6-server
    ciphers:  TLS_AES_256_GCM_SHA384,TLS_CHACHA20_POLY1305_SHA256,TLS_ECDHE_ECDSA_WITH_AES_256_GCM_SHA384,TLS_ECDHE_RSA_WITH_CHACHA20_POLY1305_SHA256,TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384
    protocol: TLS
    enabled-protocols: TLSv1.2
    client-auth: need
    trust-store: classpath:trustKeys.p12
    trust-store-password: siteweb1!
    trust-store-type: JKS
    enabled: false
  compression:
    enabled: true

knife4j:
  enable: true
  production: false

fileserver:
  rootPath: upload-dir
  whiteList:
    - jpeg
    - jpg
    - png
    - gif
    - bmp
    - tiff
    - pdf
    - doc
    - xls
    - xlsx
    - ppt
    - pptx
    - txt
    - zip
    - rar
    - tar
    - gzip
    - mp3
    - wav
    - mp4
    - json
    - xml
    - ini
    - avi
    - glb
    - obj
    - gltf
    - fbx
    - uv3d
    - 3ds
    - svg
    - webp

# cas login configuration
cas:
  serverName: http://*************:4200
  loginServer: http://*************:4200/cas
  validateServer: http://*************:4200/cas

aes-key: lmGMlJPZQBpcx3Wf
ui-core-aes-key: 9TGmCmklzogfpVRn

sensitive:
  enableFastFilter: true
  enableJackFilter: true
  enableUndoFilter: true

#app.json容器内映射路径
appjsonfile: /home/<USER>/assets/json/app.json


getui:
  appId: Wd1Jt2Mznh8bspc4ukrdy6
  appKey: Yy6SKnCHss6Ms3nYyiLzA6
  appSecret: j4paiez9np9MjjLxlScoE1
  masterSecret: CRz2rQx8w79Hn3PFd6syx9
  baseUrl: https://restapi.getui.com/v2/
api-proxy:
  connect-timeout: 1
  connection-request-timeout: 1
  read-timeout: 120

siteweb:
  uicore:
    dataEnable: {uicoreEnable}
    message:
      mqtt:
        connectionRetryTime: 10000 # 10秒
        providerConfig:
          hostUrl: tcp://mqtt:1883
          username: admin
          password: adminadmin
          clientId: s6v2_1
        consumerConfig:
          hostUrl: tcp://mqtt:1883
          username: admin
          password: adminadmin
          clientId: s6v2_2
          publishTimeOut: 3000 # 发布消息超时时间，单位毫秒
          defaultTopic:
            - gateway/data/core/users/+/+
            - gateway/data/core/roles/+/+
            - gateway/data/core/token/+
            - gateway/data/core/organizations/+/+
            - gateway/workorder/dcom/alarmorder/create/+

#是否启用双机高可用模式
ha:
  enable: false
  task-delay: 60
getsnapshot:
  user: AutoExcuter
  baseUrl: http://vpform:16666
feature-enable:
  redis-publish-subscription: true
  #是否使用去存储过程版本
  event-remove-proc: true
  #服务部署ip,在部署安装的时候会替换#server-ip
  server-ip: #server-ip
getRedisConnectState: true

# 支持sftp和ssh的采集器类别(MonitorUnitCategory)配置，多个用逗号分隔
mu:
  safe-category: 18

# 允许直接访问的路径
jwt-auth:
  exclude-paths:
    - /
    - /resources/**
    - /static/**
    - /public/**
    - /webapp/**
    - /h2-console/*
    - /configuration/**
    - /swagger-ui/**
    - /swagger-resources/**
    - /api-docs
    - /api-docs/**
    - /v2/api-docs/**
    - /**/*.html
    - /**/*.css
    - /**/*.js
    - /**/*.png
    - /**/*.jpg
    - /**/*.gif
    - /**/*.svg
    - /**/*.ttf
    - /**/*.woff
    - /assets/**
    - /**/*.eot
    - /**/*.woff2
    - /**/*.ico
    - /fontawesome-webfont.*
    - /ionicons.*
    - /roboto-v15-latin-regular.*
    - /websocket/**
    - /power.distribution/**
    - /**/coreshadows/**
    - /api/monitorunit/register
    - /api/serverstatus
    - /api/timestamp
    - /api/reportquerys/**
    - /api/external/login
    - /api/files/**
    - /api/simple/login
    - /api/sso/login
    - /api/keycloak/login
    - /api/codesso/login
    - /files/**
    - /**/health/**
    - /**/info/**
    - /**/covs/**
    - /**/covacks/**
    - /**/linkstates/**
    - /**/trace/**
    - /**/heapdump/**
    - /**/loggers/**
    - /**/liquibase/**
    - /**/logfile/**
    - /**/flyway/**
    - /**/auditevents/**
    - /**/metrics/**
    - /**/jolokia/**
    - /**/admin/**
    - /**/togglz-console/**
    - /api/captcha/**
    - /api/sendsmscode
    - /api/checksmscode
    - /api/keycloak/login
    - /api/dimensionconfigures/**
    - /api/files/dimension/**
    - /api/dimensionmodels
    - /api/checklicense
    - /api/license
    - /api/webclientlog
    - /api/energy/energyoverviewresourcestructurepuetimetype
    - /api/energy/energyapi/**
    - /api/foura/**
    - /api/systemconfig/tokenheader
    - /api/systemconfig/accountterminaldevicebindenable
    - /api/accountterminaldevicemap
    - /api/accountterminaldevicemap/bind
    - /api/uicore/*
    - /api/cqctcc/login
    - /api/cqctccapp/getUserCallback
    - /api/v1/sync/bizOrder/accountSync
    - /api/accounts/forgetpassword
    - /api/accounts/passwordinvalidtime

# 重庆电信单点登录
cqctcc-sso:
  enable: false
  ftpSeverIp: ************* # 重庆电信Ftp服务器Ip
  ftpServerPort: 30006 # 重庆电信Ftp服务器端口
  ftpUserName: netftp # 重庆电信Ftp服务器用户名
  ftpPassword: NOLZ4139##axxpwp # 重庆电信Ftp服务器密码
  ftpRetryTimes: 3 # 重庆电信Ftp重试次数
  ftpFilePath: /ftpapp/data/datafile/netftp/cqdhxt # 重庆电信Ftp文件路径
  ftpFileKeepDays: 30 # 重庆电信Ftp本地文件保留天数
  ftpDesKey: 928ae411b4c2439b85107682951f6407 # Ftp内容 DES密钥
  sysCode: CQDHXT # 重庆电信动环系统编码
  appId: test     # 信息系统应用ID
  appKey: test    # 信息系统应用KEY
  apiBaseUrl: http://*************:9200 # 人主接口基础url
  accountInfoUrl: http://*************:9202/api/v1/sync/bizOrder/hrcodeQry  # 5.1.5 改为 5.1.7 人员主账号信息新查询接口 url端口需要单独配置
  accountInfoDesKey: 788c5afa2ffb40388af7022af54dbc67  # 5.1.5. 5.1.7 人员主账号信息查询接口 DES密钥
  tokenValidateUrl: /api/openapi/RzTesttoken/tokenValidate  # 5.1.2. 统一凭证校验接口
  tokenValidateDesKey: 166377c38df04afcad3e85eea45e535f  # 5.1.2. 统一凭证校验接口 DES密钥
  loginPrivUrl: /api/openapi/RzTestPriv/loginPrivControl # 5.1.4. 主账号目标系统登录权限控制接口
  loginPrivDesKey: 49d0a02ce3294de0b23573d7ad4da4bd  # 5.1.4. 主账号目标系统登录权限控制接口 DES密钥
  loginValidateUrl: /api/openapi/RzTestlogin/loginValidate # 5.1.3. 统一登录认证接口
  loginValidateDesKey: d62f54e6fc6c497b9d82ba7e67cd69b0  # 5.1.3. 统一登录认证接口 DES密钥

# 字节跳动相关配置
byteDance:
  eventLevelFilterEnable: false  # 告警等级过滤开关

# 江苏移动巡检标准化配置
patrol:
  enableStandardSignal: false
self-diagnosis:
  equipment-category: 99 #自诊断设备类别(字节的类别值是30124)