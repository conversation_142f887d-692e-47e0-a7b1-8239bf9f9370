FROM  openjdk:17.0.2-slim-buster

RUN apt-get update
RUN apt-get install -y libxrender1 libxtst6 libxi6 libfreetype6 fontconfig ttf-wqy-microhei
RUN apt-get clean

ENV LANG C.UTF-8
ENV LC_ALL C.UTF-8
ENV LANGUAGE C.UTF-8

RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN mkdir /home/<USER>

VOLUME /tmp
WORKDIR /home/<USER>
ADD siteweb6-core-1.0-SNAPSHOT.jar /home/<USER>/siteweb6-core-1.0-SNAPSHOT.jar
EXPOSE 8200
ENTRYPOINT ["sh","-c","java -jar $JAVA_OPTS /home/<USER>/siteweb6-core-1.0-SNAPSHOT.jar $PARAMS"]