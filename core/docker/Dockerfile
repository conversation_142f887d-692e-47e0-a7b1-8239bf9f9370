FROM openjdk:17-jdk-alpine
# 更新包列表和libtasn1库、zlib库、openssl库、busybox库、libcrypto1.1库避免漏洞扫描
RUN apk update && apk upgrade libtasn1 && apk upgrade zlib && apk upgrade openssl && apk upgrade busybox && apk upgrade libcrypto1.1
RUN apk add -U tzdata
RUN cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime
RUN apk add --update font-adobe-100dpi ttf-dejavu fontconfig
RUN mkdir /home/<USER>
ENV LANG C.UTF-8
VOLUME /tmp
WORKDIR /home/<USER>
ADD siteweb6-core-1.0-SNAPSHOT.jar /home/<USER>/siteweb6-core-1.0-SNAPSHOT.jar
EXPOSE 8200
ENTRYPOINT ["sh","-c","java -jar $JAVA_OPTS /home/<USER>/siteweb6-core-1.0-SNAPSHOT.jar $PARAMS"]