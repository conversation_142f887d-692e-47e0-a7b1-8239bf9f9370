package com.siteweb.hmi.service.impl;

import com.siteweb.common.net.FtpClient;
import com.siteweb.hmi.service.ComtradeFtpService;
import com.siteweb.hmi.service.ComtradeFileCleanupService;
import com.siteweb.monitoring.entity.Equipment;
import com.siteweb.monitoring.entity.Port;
import com.siteweb.monitoring.entity.SamplerUnit;
import com.siteweb.monitoring.service.EquipmentService;
import com.siteweb.monitoring.service.PortService;
import com.siteweb.monitoring.service.SamplerUnitService;
import com.siteweb.utility.entity.SystemConfig;
import com.siteweb.utility.service.SystemConfigService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import cn.hutool.core.util.StrUtil;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Description:
 * Author: <EMAIL>
 * Creation Date: 2025/5/15
 */
@Service
@Slf4j
public class ComtradeFtpServiceImpl implements ComtradeFtpService {

    @Autowired
    private EquipmentService equipmentService;

    @Autowired
    private SamplerUnitService samplerUnitService;

    @Autowired
    private PortService portService;

    @Autowired
    private ComtradeFileCleanupService comtradeFileCleanupService;
    
    @Autowired
    private SystemConfigService systemConfigService;

    /**
     * 根据设备ID获取对应的IP地址
     * <p>
     * 该方法通过设备ID查找关联的采样单元和端口信息，从端口的setting字段中提取IP地址。
     * 查找过程：设备 -> 采样单元 -> 端口 -> 端口配置中的IP地址
     *f
     * @param equipmentId 设备ID
     * @return 设备对应的IP地址，如果任何步骤失败则返回空字符串
     */
    @Override
    public String getIpAddressByEquipmentId(Integer equipmentId) {
        // 获取设备对象
        Equipment equipment = equipmentService.findById(equipmentId);
        if (equipment == null) {
            return "";
        }

        // 获取采样单元对象
        Integer samplerUnitId = equipment.getSamplerUnitId();
        if (samplerUnitId == null) {
            return "";
        }

        SamplerUnit samplerUnit = samplerUnitService.findBySamplerUnitId(samplerUnitId);
        if (samplerUnit == null) {
            return "";
        }

        // 获取端口对象
        Integer portId = samplerUnit.getPortId();
        if (portId == null) {
            return "";
        }

        Port port = portService.findByPortIdAndMonitorUnitId(portId, samplerUnit.getMonitorUnitId());
        if (port == null) {
            return "";
        }

        // setting字段中获取IP地址
        String setting = port.getSetting();
        if (setting == null) {
            return "";
        }
        String ip = setting.replaceAll("(.*?)(\\d{1,3}\\.\\d{1,3}\\.\\d{1,3}\\.\\d{1,3})(.*)", "$2");
        return ip;
    }

    /**
     * 从指定IP地址的FTP服务器下载最新的COMTRADE文件
     * <p>
     * 该方法将只下载最近生成的COMTRADE文件，避免重复下载历史文件。
     * 具体实现待完成。
     *
     * @param ipAddress   FTP服务器的IP地址
     * @param equipmentId 设备ID
     * @return 成功下载的最新文件数量，当前返回0
     */
    @Override
    public int downloadLatestComtradeFiles(String ipAddress, Integer equipmentId) {
        if (ipAddress == null || ipAddress.isEmpty() || equipmentId == null) {
            return 0;
        }

        // 获取FTP用户名和密码
        String username = getFtpUsername();
        String password = getFtpPassword();
        
        // 创建FTP客户端，如果有用户名和密码则使用，否则使用匿名登录
        FTPClientConfig config = new FTPClientConfig(FTPClientConfig.SYST_UNIX);
        FtpClient ftpClient;
        if (StrUtil.isNotBlank(username) && StrUtil.isNotBlank(password)) {
            ftpClient = new FtpClient(ipAddress, 21, username, password, config);
        } else {
            ftpClient = new FtpClient(ipAddress, 21);
        }
        
        String localPath = "upload-dir/comtrade/" + equipmentId;
        File localDir = new File(localPath);

        if (!localDir.exists()) {
            localDir.mkdirs();
        }

        try {
            // 获取本地文件列表
            Set<String> localFiles = new HashSet<>();
            if (localDir.exists()) {
                File[] files = localDir.listFiles();
                if (files != null) {
                    for (File file : files) {
                        localFiles.add(file.getName());
                    }
                }
            } else {
                localDir.mkdirs();
            }

            // 连接FTP服务器并获取文件列表
            ftpClient.login();
            ftpClient.changeDirectory(getComtradeDirectory());
            FTPFile[] ftpFiles = ftpClient.getApacheFtpClient().listFiles();

            // 筛选需要下载的文件
            List<String> filesToDownload = new ArrayList<>();
            for (FTPFile ftpFile : ftpFiles) {
                // 忽略目录，只处理文件
                if (ftpFile.isDirectory()) {
                    continue;
                }
                
                String fileName = ftpFile.getName();
                String lowerFileName = fileName.toLowerCase();
                // 只处理.cfg和.dat文件（忽略大小写）
                if (!(lowerFileName.endsWith(".cfg") || lowerFileName.endsWith(".dat"))) {
                    continue;
                }
                
                if (!localFiles.contains(fileName)) {
                    filesToDownload.add(fileName);

                    // 确保成对下载.cfg和.dat文件（忽略大小写）
                    if (lowerFileName.endsWith(".cfg")) {
                        String baseName = fileName.substring(0, fileName.length() - 4);
                        // 查找对应的.dat文件（可能是大写或小写）
                        String datFile = findMatchingFile(ftpFiles, baseName, ".dat");
                        if (datFile != null && !localFiles.contains(datFile)) {
                            filesToDownload.add(datFile);
                        }
                    } else if (lowerFileName.endsWith(".dat")) {
                        String baseName = fileName.substring(0, fileName.length() - 4);
                        // 查找对应的.cfg文件（可能是大写或小写）
                        String cfgFile = findMatchingFile(ftpFiles, baseName, ".cfg");
                        if (cfgFile != null && !localFiles.contains(cfgFile)) {
                            filesToDownload.add(cfgFile);
                        }
                    }
                }
            }

            // 下载文件
            int downloadedCount = 0;
            for (String fileName : filesToDownload) {
                try (OutputStream outputStream = new FileOutputStream(new File(localDir, fileName))) {
                    if (ftpClient.getApacheFtpClient().retrieveFile(fileName, outputStream)) {
                        downloadedCount++;
                        log.info("下载最新COMTRADE文件成功: {}", fileName);
                    }
                }
            }

            ftpClient.logout();
            
            // 下载完成后执行文件清理
            if (downloadedCount > 0) {
                try {
                    int cleanedCount = comtradeFileCleanupService.cleanupComtradeFiles(equipmentId);
                    if (cleanedCount > 0) {
                        log.info("设备ID: {} 下载完成后清理了 {} 个旧文件对", equipmentId, cleanedCount);
                    }
                } catch (Exception e) {
                    log.error("设备ID: {} 下载后执行文件清理时出错", equipmentId, e);
                }
            }
            
            return downloadedCount;

        } catch (IOException e) {
            log.error("下载最新COMTRADE文件失败: {}", e.getMessage());
            return 0;
        }
    }

    /**
     * 获取指定设备的COMTRADE文件列表
     * <p>
     * 该方法将返回指定设备ID对应目录下的所有COMTRADE文件名列表。
     * 具体实现待完成。
     *
     * @param equipmentId 设备ID
     * @return COMTRADE文件名列表，当前返回空列表
     */
    @Override
    public List<String> getComtradeFileList(Integer equipmentId) {
        return List.of();
    }
    
    /**
     * 从系统配置中获取COMTRADE文件目录
     * 
     * @return COMTRADE文件目录，如果未配置则返回默认值"/comtrade"
     */
    private String getComtradeDirectory() {
        try {
            SystemConfig config = systemConfigService.findBySystemConfigKey("comtrade.ftp.directory");
            if (config != null && StrUtil.isNotBlank(config.getSystemConfigValue())) {
                String directory = config.getSystemConfigValue().trim();
                // 确保目录以/开头
                if (!directory.startsWith("/")) {
                    directory = "/" + directory;
                }
                log.info("从系统配置获取COMTRADE目录: {}", directory);
                return directory;
            }
        } catch (Exception e) {
            log.error("从系统配置获取COMTRADE目录失败，使用默认值", e);
        }
        // 如果获取失败，返回默认值
        return "/comtrade";
    }
    
    /**
     * 从系统配置中获取FTP用户名
     * 
     * @return FTP用户名，如果未配置则返回空字符串
     */
    private String getFtpUsername() {
        try {
            SystemConfig config = systemConfigService.findBySystemConfigKey("comtrade.ftp.username");
            if (config != null && StrUtil.isNotBlank(config.getSystemConfigValue())) {
                String username = config.getSystemConfigValue().trim();
                log.info("从系统配置获取FTP用户名: {}", username);
                return username;
            }
        } catch (Exception e) {
            log.error("从系统配置获取FTP用户名失败，使用匿名登录", e);
        }
        // 如果获取失败，返回空字符串，将使用匿名登录
        return "";
    }
    
    /**
     * 从系统配置中获取FTP密码
     * 
     * @return FTP密码，如果未配置则返回空字符串
     */
    private String getFtpPassword() {
        try {
            SystemConfig config = systemConfigService.findBySystemConfigKey("comtrade.ftp.password");
            if (config != null && StrUtil.isNotBlank(config.getSystemConfigValue())) {
                String password = config.getSystemConfigValue().trim();
                log.info("从系统配置获取FTP密码成功");
                return password;
            }
        } catch (Exception e) {
            log.error("从系统配置获取FTP密码失败，使用匿名登录", e);
        }
        // 如果获取失败，返回空字符串，将使用匿名登录
        return "";
    }

    /**
     * 查找匹配的文件名（忽略大小写）
     * 
     * @param ftpFiles FTP文件列表
     * @param baseName 基础文件名（不包含扩展名）
     * @param extension 扩展名（如".cfg"或".dat"）
     * @return 匹配的文件名，如果未找到则返回null
     */
    private String findMatchingFile(FTPFile[] ftpFiles, String baseName, String extension) {
        for (FTPFile ftpFile : ftpFiles) {
            if (ftpFile.isDirectory()) {
                continue;
            }
            String fileName = ftpFile.getName();
            String lowerFileName = fileName.toLowerCase();
            String targetFileName = (baseName + extension).toLowerCase();
            
            if (lowerFileName.equals(targetFileName)) {
                return fileName;
            }
        }
        return null;
    }
}
